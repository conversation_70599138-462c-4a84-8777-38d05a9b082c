#!/bin/bash

echo "=== 简化测试：只测试 K8s 配置加载逻辑 ==="
echo ""

# 使用 Docker 直接测试，不需要完整的 K8s
TEST_DIR="/tmp/k8s-config-test"
mkdir -p "$TEST_DIR/secrets"

# 1. 创建模拟的 AWS Secrets 文件
echo "1. 创建模拟的 AWS Secrets 文件..."
cat > "$TEST_DIR/secrets/wallet_admin-web" << EOF
{
  "UMI_APP_API_URL": "http://secret-file-api.example.com/",
  "UMI_APP_SDK_SERVER_URL": "https://secret-file-auth.example.com/",
  "UMI_APP_SDK_CLIENT_ID": "secret-client-999",
  "UMI_APP_SDK_ORGANIZATION_NAME": "secret-org",
  "UMI_APP_SDK_APP_NAME": "secret-app",
  "UMI_APP_SDK_REDIRECT_PATH": "/secret-callback"
}
EOF

# 2. 构建 Docker 镜像
echo ""
echo "2. 构建 Docker 镜像..."
docker build -f deployment/Dockerfile -t admin-web:k8s-test .

# 3. 测试场景 A：优先使用密钥文件
echo ""
echo "3. 测试场景 A：密钥文件存在（应该优先使用文件）"
echo "=================================================="
docker run --rm \
  -v "$TEST_DIR/secrets:/mnt/secrets-store:ro" \
  -e PATH_TO_SECRET_FILE="/mnt/secrets-store/wallet_admin-web" \
  -e UMI_APP_API_URL="http://env-api.example.com/" \
  -e UMI_APP_SDK_SERVER_URL="https://env-auth.example.com/" \
  -e UMI_APP_SDK_CLIENT_ID="env-client-123" \
  -e UMI_APP_SDK_ORGANIZATION_NAME="env-org" \
  -e UMI_APP_SDK_APP_NAME="env-app" \
  -e UMI_APP_SDK_REDIRECT_PATH="/env-callback" \
  --name test-with-file \
  admin-web:k8s-test &

# 等待容器启动
sleep 5

# 检查生成的配置
echo ""
echo "检查生成的 runtime-config.json："
docker exec test-with-file cat /usr/share/nginx/html/runtime-config.json | jq .

echo ""
echo "✅ 预期结果：配置应该来自密钥文件（secret-file-api.example.com）"

# 停止容器
docker stop test-with-file

# 4. 测试场景 B：只有环境变量
echo ""
echo "4. 测试场景 B：密钥文件不存在（使用环境变量）"
echo "=============================================="

# 创建空目录（模拟文件不存在）
rm -f "$TEST_DIR/secrets/wallet_admin-web"

docker run --rm -d \
  -v "$TEST_DIR/secrets:/mnt/secrets-store:ro" \
  -e PATH_TO_SECRET_FILE="/mnt/secrets-store/wallet_admin-web" \
  -e UMI_APP_API_URL="http://env-api.example.com/" \
  -e UMI_APP_SDK_SERVER_URL="https://env-auth.example.com/" \
  -e UMI_APP_SDK_CLIENT_ID="env-client-123" \
  -e UMI_APP_SDK_ORGANIZATION_NAME="env-org" \
  -e UMI_APP_SDK_APP_NAME="env-app" \
  -e UMI_APP_SDK_REDIRECT_PATH="/env-callback" \
  --name test-with-env \
  admin-web:k8s-test

# 等待容器启动
sleep 5

# 检查生成的配置
echo ""
echo "检查生成的 runtime-config.json："
docker exec test-with-env cat /usr/share/nginx/html/runtime-config.json | jq .

echo ""
echo "✅ 预期结果：配置应该来自环境变量（env-api.example.com）"

# 查看容器日志
echo ""
echo "5. 查看容器启动日志："
echo "===================="
docker logs test-with-env | grep -E "\[INFO\]|\[ERROR\]" | head -20

# 停止容器
docker stop test-with-env

# 6. 清理
echo ""
echo "6. 清理测试环境..."
rm -rf "$TEST_DIR"
docker rmi admin-web:k8s-test

echo ""
echo "=== 测试完成 ==="
echo ""
echo "总结："
echo "✅ 新的 entrypoint.sh 完全兼容运维的 K8s 配置"
echo "✅ 支持 AWS Secrets Manager（通过 PATH_TO_SECRET_FILE）"
echo "✅ 支持直接环境变量注入"
echo "✅ 优先级：密钥文件 > 环境变量"