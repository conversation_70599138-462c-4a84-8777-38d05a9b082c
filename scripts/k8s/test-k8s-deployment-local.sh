#!/bin/bash

echo "=== 在本地 Minikube 测试 K8s 部署 ==="
echo ""

# 检查 Minikube 是否运行
if ! minikube status | grep -q "Running"; then
    echo "❌ Minikube 未运行，请先运行: ./setup-minikube.sh"
    exit 1
fi

# 创建测试目录
TEST_DIR="./k8s-test"
mkdir -p "$TEST_DIR"

# 1. 创建命名空间
echo "1. 创建命名空间..."
kubectl create namespace admin-web --dry-run=client -o yaml > "$TEST_DIR/namespace.yaml"
kubectl apply -f "$TEST_DIR/namespace.yaml"

# 2. 创建 ServiceAccount（简化版）
echo "2. 创建 ServiceAccount..."
cat > "$TEST_DIR/serviceaccount.yaml" << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: admin-web-sa
  namespace: admin-web
EOF
kubectl apply -f "$TEST_DIR/serviceaccount.yaml"

# 3. 创建模拟的密钥（代替 AWS Secrets Manager）
echo "3. 创建模拟密钥..."
cat > "$TEST_DIR/mock-secrets.json" << EOF
{
  "UMI_APP_API_URL": "http://mock-api.local/",
  "UMI_APP_SDK_SERVER_URL": "https://mock-auth.local/",
  "UMI_APP_SDK_CLIENT_ID": "mock-client-id-123",
  "UMI_APP_SDK_ORGANIZATION_NAME": "mock-org",
  "UMI_APP_SDK_APP_NAME": "mock-app",
  "UMI_APP_SDK_REDIRECT_PATH": "/mock-callback"
}
EOF

# 创建 ConfigMap 来模拟密钥文件
kubectl create configmap admin-web-secrets \
  --from-file=wallet_admin-web="$TEST_DIR/mock-secrets.json" \
  -n admin-web \
  --dry-run=client -o yaml > "$TEST_DIR/configmap.yaml"
kubectl apply -f "$TEST_DIR/configmap.yaml"

# 4. 构建 Docker 镜像（使用 Minikube 的 Docker）
echo "4. 构建 Docker 镜像..."
eval $(minikube docker-env)
docker build -f deployment/Dockerfile -t admin-web:test .

# 5. 创建本地化的 Deployment
echo "5. 创建 Deployment..."
cat > "$TEST_DIR/deployment.yaml" << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-web
  namespace: admin-web
  labels:
    app: admin-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin-web
  template:
    metadata:
      labels:
        app: admin-web
    spec:
      serviceAccountName: admin-web-sa
      # 模拟 AWS Secrets 挂载
      volumes:
        - name: secrets-store-inline
          configMap:
            name: admin-web-secrets
      containers:
        - name: admin-web
          image: admin-web:test
          imagePullPolicy: Never  # 使用本地镜像
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          env:
            # 模拟运维的环境变量配置
            - name: PATH_TO_SECRET_FILE
              value: /mnt/secrets-store/wallet_admin-web
            - name: UMI_APP_API_URL
              value: http://api-admin-dev.jjpay.co/
            - name: UMI_APP_SDK_SERVER_URL
              value: https://admin-dev.jjpay.co/
            - name: UMI_APP_SDK_CLIENT_ID
              value: 47637dde487603346a77
            - name: UMI_APP_SDK_ORGANIZATION_NAME
              value: xpay
            - name: UMI_APP_SDK_APP_NAME
              value: application_xpay
            - name: UMI_APP_SDK_REDIRECT_PATH
              value: /callback
            - name: APP_NAME
              value: admin_api
          volumeMounts:
            - name: secrets-store-inline
              readOnly: true
              mountPath: /mnt/secrets-store
EOF

kubectl apply -f "$TEST_DIR/deployment.yaml"

# 6. 创建 Service
echo "6. 创建 Service..."
cat > "$TEST_DIR/service.yaml" << EOF
apiVersion: v1
kind: Service
metadata:
  name: admin-web
  namespace: admin-web
spec:
  selector:
    app: admin-web
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: NodePort
EOF
kubectl apply -f "$TEST_DIR/service.yaml"

# 7. 等待部署完成
echo ""
echo "7. 等待部署完成..."
kubectl wait --for=condition=available --timeout=120s deployment/admin-web -n admin-web

# 8. 查看状态
echo ""
echo "8. 部署状态："
kubectl get all -n admin-web

# 9. 查看日志
echo ""
echo "9. 查看容器日志："
echo "-----------------------------------"
kubectl logs -n admin-web -l app=admin-web --tail=50

# 10. 测试配置加载
echo ""
echo "10. 测试配置加载："
echo "-----------------------------------"
POD_NAME=$(kubectl get pods -n admin-web -l app=admin-web -o jsonpath='{.items[0].metadata.name}')
if [ -n "$POD_NAME" ]; then
    echo "检查 runtime-config.json 是否生成："
    kubectl exec -n admin-web "$POD_NAME" -- cat /usr/share/nginx/html/runtime-config.json 2>/dev/null | jq . || echo "文件未找到或格式错误"
    
    echo ""
    echo "检查挂载的密钥文件："
    kubectl exec -n admin-web "$POD_NAME" -- ls -la /mnt/secrets-store/ 2>/dev/null || echo "目录未找到"
fi

# 11. 获取访问地址
echo ""
echo "11. 访问应用："
echo "-----------------------------------"
SERVICE_URL=$(minikube service admin-web -n admin-web --url)
echo "应用地址: $SERVICE_URL"
echo ""
echo "你可以运行以下命令测试："
echo "curl $SERVICE_URL/runtime-config.json"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "有用的命令："
echo "- 查看日志: kubectl logs -n admin-web -l app=admin-web -f"
echo "- 进入容器: kubectl exec -it -n admin-web $POD_NAME -- /bin/sh"
echo "- 删除测试: kubectl delete namespace admin-web"
echo "- 清理镜像: eval \$(minikube docker-env) && docker rmi admin-web:test"