#!/bin/bash

echo "=== 清理 K8s 测试资源 ==="
echo ""

# 1. 清理 K8s 资源
echo "1. 清理 Kubernetes 资源..."
if kubectl get namespace admin-web &>/dev/null; then
    echo "   删除 namespace admin-web（这会删除所有相关资源）..."
    kubectl delete namespace admin-web
    echo "   ✅ Kubernetes 资源已清理"
else
    echo "   ✅ 没有找到 admin-web namespace"
fi

# 2. 清理 Docker 镜像
echo ""
echo "2. 清理 Docker 镜像..."

# 先停止所有使用测试镜像的容器
echo "   停止相关容器..."
docker ps -a | grep "admin-web" | awk '{print $1}' | xargs -r docker stop
docker ps -a | grep "admin-web" | awk '{print $1}' | xargs -r docker rm

# 删除测试镜像
echo "   删除测试镜像..."
docker rmi admin-web:test 2>/dev/null && echo "   ✅ 删除 admin-web:test"
docker rmi admin-web:k8s-test 2>/dev/null && echo "   ✅ 删除 admin-web:k8s-test"

# 3. 清理测试目录
echo ""
echo "3. 清理测试目录..."
rm -rf ./k8s-test 2>/dev/null && echo "   ✅ 删除 k8s-test 目录"
rm -rf ./k8s-simulation 2>/dev/null && echo "   ✅ 删除 k8s-simulation 目录"

# 4. 清理 Minikube（如果需要）
echo ""
echo "4. Minikube 状态..."
if command -v minikube &> /dev/null; then
    if minikube status | grep -q "Running"; then
        echo "   Minikube 正在运行"
        echo "   如果要停止 Minikube，运行: minikube stop"
        echo "   如果要删除 Minikube，运行: minikube delete"
    else
        echo "   ✅ Minikube 未运行"
    fi
else
    echo "   ✅ Minikube 未安装"
fi

# 5. 清理 Docker 系统（可选）
echo ""
echo "5. Docker 系统清理..."
echo "   清理未使用的容器、网络、镜像..."
docker system prune -f
echo "   ✅ Docker 系统已清理"

# 6. 显示剩余资源
echo ""
echo "=== 清理完成 ==="
echo ""
echo "剩余 Docker 镜像:"
docker images | grep -E "admin-web|REPOSITORY" || echo "没有相关镜像"

echo ""
echo "剩余容器:"
docker ps -a | grep -E "admin-web|CONTAINER" || echo "没有相关容器"

echo ""
echo "K8s namespaces:"
kubectl get namespaces | grep -E "admin-web|NAME" || echo "没有相关 namespace"

echo ""
echo "磁盘空间:"
df -h | grep -E "/$|Filesystem"