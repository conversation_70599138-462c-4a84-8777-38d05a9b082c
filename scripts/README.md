# Scripts Directory

This directory contains various utility scripts for testing and development.

## Directory Structure

```
scripts/
├── testing/      # Configuration and system testing scripts
├── k8s/         # Kubernetes deployment and testing scripts
└── config/      # Configuration management scripts
```

## Scripts Overview

### testing/

- `test-new-config-system.sh` - Tests the new configuration management system
- `test-dual-config-mode.sh` - Tests dual configuration mode (file + env vars)
- `test-config-priority.py` - Python script to test configuration priority
- `verify-k8s-config.sh` - Verifies K8s configuration compatibility

### k8s/

- `setup-minikube.sh` - Sets up Minikube for local K8s testing
- `cleanup-minikube.sh` - Cleans up Minikube installation
- `cleanup-k8s-test.sh` - Cleans up K8s test resources
- `test-k8s-deployment-local.sh` - Tests K8s deployment locally
- `test-k8s-config-only.sh` - Tests K8s configuration loading
- `test-k8s-with-docker-compose.sh` - Tests K8s-like setup with <PERSON><PERSON> Compose

## Usage

All scripts are executable. To run any script:

```bash
./scripts/k8s/setup-minikube.sh
./scripts/testing/test-new-config-system.sh
```

## Note

These scripts were created during the development of the runtime configuration system and K8s deployment compatibility. They are kept for reference and future testing needs.
