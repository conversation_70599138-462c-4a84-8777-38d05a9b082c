# 配置优先级修复说明

## 问题描述

运维反馈在 GitLab CI/CD 构建过程中，传入的 `config_variables.json` 没有正确在镜像中使用。

## 问题分析

从构建日志分析发现：

1. **config_variables.json 中的配置值：**

   ```json
   {
     "UMI_APP_API_URL": "http://**************:7999",
     "UMI_APP_SDK_SERVER_URL": "http://**************:8000",
     "UMI_APP_SDK_CLIENT_ID": "80756163da773c15fa03"
   }
   ```

2. **但生成的 Conf.ts 中显示的值：**

   ```typescript
   apiUrl: "https://api-admin-dev.jjpay.co",
   serverUrl: "https://sso-dev.jjpay.co",
   clientId: "47637dde487603346a77",
   ```

3. **根本原因：**
   - GitLab CI/CD 环境中已经预设了这些环境变量
   - 原来的 Dockerfile 逻辑会被环境变量覆盖，没有考虑优先级

## 解决方案

修改 Dockerfile 实现正确的配置优先级：

### 新的优先级逻辑

1. **环境变量优先** (最高优先级)

   - 来源：CI/CD 系统、Kubernetes、docker-compose 等
   - 适用场景：生产环境、不同环境的配置管理

2. **config_variables.json 作为默认值** (较低优先级)
   - 来源：项目中的配置文件
   - 适用场景：开发环境、默认配置

### 修改内容

**修改前的问题：**

```dockerfile
# 原来的逻辑会清除环境变量，强制使用配置文件
unset UMI_APP_API_URL UMI_APP_SDK_SERVER_URL ...
eval $(jq -r '...' config_variables.json | sed 's/^/export /')
```

**修改后的正确逻辑：**

```dockerfile
# 新逻辑：环境变量优先，配置文件作为默认值
while IFS='=' read -r key value; do
    if [ -z "${!key:-}" ]; then
        export "$key=$value"
        echo "Set $key=$value (from config file)"
    else
        echo "Using existing $key=${!key} (from environment)"
    fi
done < <(jq -r '...' config_variables.json)
```

## 预期结果

修复后，构建日志将显示：

```
Loading configuration with environment variable priority...
Using existing UMI_APP_API_URL=https://api-admin-dev.jjpay.co (from environment)
Using existing UMI_APP_SDK_SERVER_URL=https://sso-dev.jjpay.co (from environment)
Using existing UMI_APP_SDK_CLIENT_ID=47637dde487603346a77 (from environment)
Set UMI_APP_SDK_ORGANIZATION_NAME=organization_xpay (from config file)
Set UMI_APP_SDK_APP_NAME=application_xpay (from config file)
Set UMI_APP_SDK_REDIRECT_PATH=/callback (from config file)
```

这样：

- 运维通过 CI/CD 设置的环境变量会被正确使用
- 没有在环境中设置的变量会使用配置文件中的默认值
- 构建日志清楚显示每个变量的来源

## 部署说明

1. 这个修复已经应用到 `deployment/Dockerfile`
2. 运维需要重新触发 GitLab CI/CD 构建流水线
3. 新的构建将正确使用环境变量优先级
4. 可以通过构建日志验证配置来源和最终值
