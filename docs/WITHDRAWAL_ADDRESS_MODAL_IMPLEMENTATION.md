# 提现记录地址展示模态框实现文档

## 概述

本文档记录了提现记录列表中地址展示模态框的优化实现，主要针对法币类型提现的特殊处理。

## 需求背景

- 原有系统只支持加密货币提现地址的展示
- 新增法币提现功能后，需要区分展示不同类型的提现信息
- 法币提现包括：支付宝账号、支付宝二维码、微信二维码三种类型

## 实现方案

### 1. 数据模型更新

#### 文件：`src/pages/Admin/FundWithdrawalsManagement/types.ts`

```typescript
// 新增法币提现类型枚举
export enum FiatWithdrawalType {
  AlipayAccount = 'alipay_account', // 支付宝账号
  AlipayQR = 'alipay_qr',           // 支付宝二维码
  WechatQR = 'wechat_qr',           // 微信二维码
}

// 更新 FundWithdrawalRecord 接口，添加法币相关字段
export interface FundWithdrawalRecord {
  // ... 原有字段
  fiatType?: string;           // 法币提现类型
  recipientName?: string;      // 法币收款人姓名
  recipientAccount?: string;   // 法币收款账户
  recipientQrcode?: string;    // 法币收款二维码
}
```

### 2. 创建统一的地址详情模态框组件

#### 文件：`src/pages/Admin/FundWithdrawalsManagement/components/AddressDetailModal.tsx`

主要功能：
- 智能判断提现类型（通过 `fiatType` 字段）
- 根据类型展示不同的内容：
  - **加密货币提现**：显示地址二维码、币种、链、金额等信息
  - **法币提现-支付宝账号**：显示收款人姓名、账号、金额等信息
  - **法币提现-二维码类型**：显示二维码图片、收款人信息、金额等

关键实现：
```typescript
// 判断是否为法币提现
const isFiatWithdrawal = !!record.fiatType;

// 获取法币类型的中文名称
const getFiatTypeName = (fiatType?: string) => {
  const typeMap: Record<string, string> = {
    [FiatWithdrawalType.AlipayAccount]: '支付宝账号',
    [FiatWithdrawalType.AlipayQR]: '支付宝二维码',
    [FiatWithdrawalType.WechatQR]: '微信二维码',
  };
  return typeMap[fiatType || ''] || '未知类型';
};
```

### 3. 更新地址列渲染逻辑

#### 文件：`src/pages/Admin/FundWithdrawalsManagement/constants.tsx`

```typescript
// 添加法币类型映射常量
export const FIAT_TYPE_MAP: Record<string, string> = {
  [FiatWithdrawalType.AlipayAccount]: '支付宝账号',
  [FiatWithdrawalType.AlipayQR]: '支付宝二维码',
  [FiatWithdrawalType.WechatQR]: '微信二维码',
};

// 更新地址列的渲染逻辑
{
  title: '提现地址',
  dataIndex: 'address',
  key: 'address',
  render: (text, record) => {
    // 判断是否为法币提现
    if (record.fiatType) {
      const typeName = FIAT_TYPE_MAP[record.fiatType] || '法币提现';
      return (
        <a onClick={() => handleAddressClick(record)}>
          {typeName}
        </a>
      );
    }
    // 加密货币提现
    if (!text) return '-';
    return (
      <a onClick={() => handleAddressClick(record)}>
        {text}
      </a>
    );
  },
}
```

### 4. 状态管理更新

#### 文件：`src/pages/Admin/FundWithdrawalsManagement/hooks.ts`

添加地址模态框相关状态和处理函数：
```typescript
// 地址详情模态框相关状态
const [addressModalVisible, setAddressModalVisible] = useState(false);
const [addressRecord, setAddressRecord] = useState<FundWithdrawalRecord | null>(null);

// 点击地址查看详情
const handleAddressClick = (record: FundWithdrawalRecord) => {
  setAddressRecord(record);
  setAddressModalVisible(true);
};

// 关闭地址详情模态框
const handleAddressModalClose = () => {
  setAddressModalVisible(false);
  setAddressRecord(null);
};
```

### 5. 主组件集成

#### 文件：`src/pages/Admin/FundWithdrawalsManagement/index.tsx`

```typescript
// 引入新组件
import { AddressDetailModal } from './components';

// 在组件中添加模态框
<AddressDetailModal
  visible={addressModalVisible}
  record={addressRecord}
  onClose={handleAddressModalClose}
/>
```

## 修改文件清单

1. `/src/pages/Admin/FundWithdrawalsManagement/types.ts` - 添加法币类型定义
2. `/src/pages/Admin/FundWithdrawalsManagement/components/AddressDetailModal.tsx` - 新建地址详情模态框组件
3. `/src/pages/Admin/FundWithdrawalsManagement/components/index.ts` - 导出新组件
4. `/src/pages/Admin/FundWithdrawalsManagement/constants.tsx` - 更新地址列渲染逻辑，添加法币类型映射
5. `/src/pages/Admin/FundWithdrawalsManagement/hooks.ts` - 添加地址模态框状态管理
6. `/src/pages/Admin/FundWithdrawalsManagement/index.tsx` - 集成地址详情模态框

## 使用说明

1. **加密货币提现**：点击地址后显示二维码和详细信息
2. **法币提现**：
   - 列表中显示提现类型（支付宝账号/支付宝二维码/微信二维码）
   - 点击后显示相应的账户信息或二维码图片

## 扩展性

如需添加新的法币提现类型：
1. 在 `FiatWithdrawalType` 枚举中添加新类型
2. 在 `FIAT_TYPE_MAP` 中添加中文映射
3. 根据需要在 `AddressDetailModal` 中调整展示逻辑

## 注意事项

- 二维码图片通过 `recipientQrcode` 字段获取，需确保后端返回正确的图片URL
- 所有账号信息都支持一键复制功能
- 模态框支持点击遮罩关闭，提升用户体验