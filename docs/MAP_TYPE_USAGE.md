# MAP 类型使用说明

## 概述

新增了 MAP 类型，类似于 Go 语言中的 map，用于存储键值对数据。

## 功能特性

### 1. 动态键值对编辑

- 支持添加/删除键值对
- 实时预览和编辑
- 自动 JSON 格式化存储

### 2. 数据格式

- 存储格式：JSON 字符串
- 显示格式：可视化键值对编辑器
- 支持的值类型：字符串

### 3. 使用场景示例

#### 客服联系信息

```
键名: 客服1    值: 1
键名: 客服2    值: 2
键名: 客服3    值: 3
```

存储为：`{"客服1":"1","客服2":"2","客服3":"3"}`

#### 系统配置映射

```
键名: max_connections    值: 100
键名: timeout           值: 30
键名: retry_count       值: 3
```

存储为：`{"max_connections":"100","timeout":"30","retry_count":"3"}`

## 技术实现

### 1. 类型定义

在 `types.ts` 中添加了 `MAP: 'map'` 类型。

### 2. 输入组件

`MapInputRenderer` 组件提供：

- 键值对的可视化编辑
- 添加/删除功能
- 实时验证
- JSON 格式转换

### 3. 验证规则

- 确保输出为有效的 JSON 对象
- 不允许数组或 null 值
- 键名不能为空

## 使用方法

1. 在配置管理中选择值类型为 "MAP"
2. 使用可视化编辑器添加键值对
3. 系统自动转换为 JSON 格式存储
4. 编辑时自动解析并显示为键值对形式

## 注意事项

- 所有值都会被转换为字符串类型
- 键名不能包含特殊字符，建议使用字母、数字和下划线
- 删除操作会保留至少一个空的键值对用于添加新项
