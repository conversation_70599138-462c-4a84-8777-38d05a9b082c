# Telegram 用户名显示修复

## 问题

- 接口返回的 telegramUsername 字段可能已经包含@符号（如"@smoothly9"）
- 组件之前又添加了一个@，导致显示为"@@smoothly9"

## 解决方案

修改了`display.tsx`中的`TelegramInfoDisplay`组件：

```typescript
if (type === 'username') {
  // 移除@前缀（如果存在），因为接口可能返回带@的用户名
  const username = value.startsWith('@') ? value.substring(1) : value;
  return (
    <Link
      href={`https://t.me/${username}`}
      target="_blank"
      onClick={(e) => e.stopPropagation()}
    >
      {value} // 显示原始值
    </Link>
  );
}
```

## 效果

- 如果接口返回"@smoothly9"，显示为"@smoothly9"
- 如果接口返回"smoothly9"，显示为"smoothly9"
- 链接 URL 始终使用不带@的用户名，确保跳转正确

## 注意

- 同时我注意到`constants.ts`中的 displayFormat 已经从'link'改为'text'
- 这是正确的，因为我们在 display 组件中已经处理了链接展示
- 这样可以避免类型定义的混淆
