# AWS Secrets Manager 配置文件格式

## 文件位置

当使用 Kubernetes Secrets Store CSI Driver 时，AWS Secrets Manager 的密钥会被挂载为文件：

- 路径：`/mnt/secrets-store/wallet_admin-web`
- 格式：JSON

## 文件格式要求

### 标准格式

```json
{
  "UMI_APP_API_URL": "http://api-admin-dev.jjpay.co/",
  "UMI_APP_SDK_SERVER_URL": "https://admin-dev.jjpay.co/",
  "UMI_APP_SDK_CLIENT_ID": "47637dde487603346a77",
  "UMI_APP_SDK_ORGANIZATION_NAME": "xpay",
  "UMI_APP_SDK_APP_NAME": "application_xpay",
  "UMI_APP_SDK_REDIRECT_PATH": "/callback"
}
```

### 必需的字段

- `UMI_APP_API_URL` - API 服务器地址
- `UMI_APP_SDK_SERVER_URL` - Casdoor 认证服务器地址
- `UMI_APP_SDK_CLIENT_ID` - OAuth 客户端 ID
- `UMI_APP_SDK_ORGANIZATION_NAME` - Casdoor 组织名称
- `UMI_APP_SDK_APP_NAME` - Casdoor 应用名称
- `UMI_APP_SDK_REDIRECT_PATH` - OAuth 回调路径

## 日志输出示例

### 使用 AWS Secrets 文件时的日志

```
[INFO] 2025-06-15 20:00:00 - Starting frontend configuration...
[INFO] 2025-06-15 20:00:00 - Found secrets file at '/mnt/secrets-store/wallet_admin-web', using file-based configuration
[INFO] 2025-06-15 20:00:00 - File details:
[INFO] 2025-06-15 20:00:00 - - Size: 256 bytes
[INFO] 2025-06-15 20:00:00 - - Permissions: -r--------
[INFO] 2025-06-15 20:00:00 - File content preview (first 500 chars, values masked):
{
  "UMI_APP_API_URL": "***",
  "UMI_APP_SDK_SERVER_URL": "***",
  "UMI_APP_SDK_CLIENT_ID": "***",
  "UMI_APP_SDK_ORGANIZATION_NAME": "***",
  "UMI_APP_SDK_APP_NAME": "***",
  "UMI_APP_SDK_REDIRECT_PATH": "***"
}
[INFO] 2025-06-15 20:00:00 - Found 6 configuration variables in the file
[INFO] 2025-06-15 20:00:00 - Loaded from file: UMI_APP_API_URL=*** (value hidden for security)
[INFO] 2025-06-15 20:00:00 - Loaded from file: UMI_APP_SDK_SERVER_URL=*** (value hidden for security)
...
[INFO] 2025-06-15 20:00:00 - Successfully loaded 6 configuration variables from secrets file
```

### 使用环境变量时的日志

```
[INFO] 2025-06-15 20:00:00 - Starting frontend configuration...
[INFO] 2025-06-15 20:00:00 - No secrets file found at '/mnt/secrets-store/wallet_admin-web', using environment variables
[INFO] 2025-06-15 20:00:00 - Available environment variables:
[INFO] 2025-06-15 20:00:00 - - UMI_APP_API_URL=*** (from environment)
[INFO] 2025-06-15 20:00:00 - - UMI_APP_SDK_SERVER_URL=*** (from environment)
...
```

## 安全说明

- 配置值在日志中会被隐藏（显示为 `***`）
- 只记录配置键名，不记录实际值
- 文件内容预览会对敏感值进行脱敏处理

## 故障排查

如果看到以下错误：

```
[ERROR] Failed to parse JSON secrets file '/mnt/secrets-store/wallet_admin-web'
[ERROR] Please ensure the file contains valid JSON format
```

请检查：

1. 文件是否存在且可读
2. 文件内容是否为有效的 JSON 格式
3. AWS Secrets Manager 中的密钥配置是否正确
