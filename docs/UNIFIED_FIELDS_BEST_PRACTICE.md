# 统一搜索和展示组件最佳实践

## 概述

我们创建了一个完整的统一字段管理系统，实现了搜索和展示的成对配置。这个系统大大简化了代码，提高了可维护性。

## 核心特性

### 1. 成对配置

- 搜索和展示通过单一配置对象控制
- 每个字段可以独立配置是否在搜索和表格中显示

### 2. 统一的展示风格

- **代理信息**：使用不同颜色的 Tag 展示（蓝色、绿色、橙色）
- **Telegram 用户名**：展示为可点击的链接（自动处理@符号）
- **空值处理**：统一显示"-"或"无"

### 3. 类型安全

- 完整的 TypeScript 支持
- 自动类型推导

## 使用示例

### 基础用法

```typescript
import { useUnifiedFields } from '@/components/UnifiedFields';

const columns = useUnifiedFields(
  baseColumns,
  {
    dateRange: { search: true, display: false },
    firstAgent: { search: false, display: false },
    secondAgent: { search: false, display: false },
    thirdAgent: { search: false, display: false },
    telegramId: { search: true, display: true },
    telegramUsername: { search: true, display: true },
    firstName: { search: true, display: true },
  },
  undefined, // 自定义渲染函数
  2, // 在第2个位置后插入（账号、昵称之后）
);
```

### 控制字段插入位置

```typescript
// 在所有基础列之前
const columns = useUnifiedFields(baseColumns, config, undefined, 'before');

// 在所有基础列之后
const columns = useUnifiedFields(baseColumns, config, undefined, 'after');

// 在指定位置插入（推荐）
const columns = useUnifiedFields(baseColumns, config, undefined, 2);
```

### 简化配置

如果搜索和展示配置相同，可以直接使用布尔值：

```typescript
const columns = useUnifiedFields(baseColumns, {
  dateRange: false, // 不搜索，不展示
  firstAgent: true, // 搜索且展示
  secondAgent: true,
  thirdAgent: true,
  telegramId: true,
  telegramUsername: true,
  firstName: true,
});
```

### 自定义渲染

```typescript
import { UnifiedFieldType } from '@/types/search';

const columns = generateUnifiedColumns({
  fields: {
    firstAgent: true,
    telegramUsername: true,
  },
  customRenders: {
    [UnifiedFieldType.AGENT_FIRST]: (value) => (
      <Tag color="purple" icon={<UserOutlined />}>
        {value || '无'}
      </Tag>
    ),
  },
  additionalColumns: baseColumns,
});
```

## 组件架构

### 1. 类型定义 (`/src/types/search.ts`)

- `UnifiedFieldType`: 字段类型枚举
- `UnifiedFieldDefinition`: 字段元数据定义
- `UnifiedFieldsConfig`: 配置接口

### 2. 常量定义 (`/src/components/UnifiedFields/constants.ts`)

- 所有字段的元数据定义
- 代理层级颜色配置
- 工具函数

### 3. 展示组件 (`/src/components/UnifiedFields/display.tsx`)

- `AgentInfoDisplay`: 代理信息展示
- `TelegramInfoDisplay`: Telegram 信息展示
- `UnifiedFieldDisplay`: 通用展示组件

### 4. 搜索组件 (`/src/components/UnifiedFields/search.tsx`)

- 从统一配置生成搜索字段
- 日期范围特殊处理

### 5. 主入口 (`/src/components/UnifiedFields/index.tsx`)

- `generateUnifiedColumns`: 生成完整列配置
- `useUnifiedFields`: React Hook 便捷函数

## 新旧对比

### 旧代码（重复定义）

```typescript
// 搜索字段定义
{
  title: '一级代理',
  dataIndex: 'firstAgentName',
  hideInTable: true,
},
// 展示字段定义
{
  title: '一级代理',
  dataIndex: 'firstAgentName',
  hideInSearch: true,
  render: (text) => text || '-',
},
```

### 新代码（统一配置）

```typescript
const columns = useUnifiedFields(baseColumns, {
  firstAgent: true, // 一次配置，两处生效
});
```

## 效果展示

### 搜索区域

- 所有配置的搜索字段自动生成
- 统一的 placeholder 提示
- 日期范围选择器

### 表格展示

- 代理信息：彩色 Tag 展示
- Telegram 用户名：可点击链接
- 空值处理：统一显示样式

## 最佳实践

1. **模块化配置**：每个模块根据需要选择展示的字段
2. **保持一致性**：所有模块使用相同的展示风格
3. **性能优化**：使用 useMemo 缓存列配置
4. **扩展性**：可以轻松添加新的统一字段

## 后续计划

1. 将此模式应用到所有 16 个模块
2. 支持更多字段类型（如状态、金额等）
3. 添加字段排序功能
4. 支持响应式布局
