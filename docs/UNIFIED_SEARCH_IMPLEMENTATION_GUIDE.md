# 统一搜索条件集成指南

## 概述

本指南说明如何将统一搜索条件集成到管理后台的各个模块中。

## 已完成的工作

### 1. 创建了统一搜索字段类型定义

- 文件位置：`src/types/search.ts`
- 定义了 `UnifiedAgentSearchFields` 接口
- 提供了日期范围格式化工具函数

### 2. 创建了通用搜索组件

- 文件位置：`src/components/AgentSearchFields/index.tsx`
- 提供了 `generateAgentSearchColumns` 函数
- 支持灵活配置显示哪些搜索字段

### 3. 集成到用户管理模块

- 文件位置：`src/pages/Admin/UserManagement/index.tsx`
- 添加了所有统一搜索字段
- 保持原有功能不变

## 集成步骤

### 1. 导入必要的类型和组件

```typescript
import { mergeSearchColumns } from '@/components/AgentSearchFields';
import type { UnifiedAgentSearchFields } from '@/types/search';
```

### 2. 修改页面的数据类型

确保你的数据类型继承自 `UnifiedAgentSearchFields`：

```typescript
export interface YourDataType extends BaseType, UnifiedAgentSearchFields {
  // 其他字段...
}
```

### 3. 分离基础列和搜索列

```typescript
const baseColumns = useMemo<ProColumns<YourDataType>[]>(
  () => [
    // 你的表格显示列（不包含搜索字段）
    {
      title: '字段名',
      dataIndex: 'fieldName',
      hideInSearch: true, // 重要：避免重复
    },
    // ...
  ],
  [],
);
```

### 4. 合并搜索列

```typescript
const columns = useMemo(() => {
  return mergeSearchColumns(baseColumns, {
    showDateRange: true,
    showFirstAgent: true,
    showSecondAgent: true,
    showThirdAgent: true,
    showTelegramId: true,
    showTelegramUsername: true,
    showFirstName: true,
  });
}, [baseColumns]);
```

### 5. 在 ProTable 中使用

```typescript
<ProTable<YourDataType>
  columns={columns}
  request={yourRequestFunction}
  // 其他配置...
/>
```

## 搜索字段配置选项

- `showDateRange`: 显示日期范围选择器
- `showFirstAgent`: 显示一级代理搜索
- `showSecondAgent`: 显示二级代理搜索
- `showThirdAgent`: 显示三级代理搜索
- `showTelegramId`: 显示 Telegram ID 搜索
- `showTelegramUsername`: 显示 Telegram 用户名搜索
- `showFirstName`: 显示名字搜索

## 待完成的模块

1. 登录日志管理 (LoginLogManagement)
2. 交易记录管理 (TransactionManagement)
3. 用户充值管理 (UserRechargeManagement)
4. 用户提现管理 (UserWithdrawManagement)
5. 转账记录管理 (TransferManagement)
6. 红包管理 (RedPacketManagement)
7. 支付请求管理 (PaymentRequestManagement)
8. 兑换订单管理 (ExchangeOrderManagement)
9. 其他相关模块...

## 注意事项

1. 确保 API 接口已经支持这些搜索参数
2. 日期范围格式必须是 "YYYY-MM-DD,YYYY-MM-DD"
3. 所有文本搜索都是模糊搜索
4. 避免在 baseColumns 中重复定义搜索字段
