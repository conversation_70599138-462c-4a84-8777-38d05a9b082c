# Deployment Guide

This directory contains all the necessary files for deploying the X-Game Admin Web application using Docker.

## Directory Structure

- `Dockerfile`: Defines the build process for the admin-web application
- `nginx.conf`: Nginx configuration for serving the web application
- `.dockerignore`: Files to exclude from Docker build context
- `deploy.sh`: Deployment script for easy deployment
- `docker-compose.yml`: Docker Compose configuration for deployment

## Environment Variables

The following environment variables can be set to configure the deployment:

- `API_URL`: URL of the backend API service (default: http://**************:7999)
- `UMI_APP_API_URL`: URL for the UMI application (set automatically based on API_URL)

These variables are defined in the `config_variables.json` file and are used to generate the `Conf.ts` file during the build process.

## Deployment Steps

1. Make sure Docker and Docker Compose are installed on your system
2. Run the deployment script with optional parameters:

```bash
# Deploy with default settings
./deployment/deploy.sh

# Deploy with custom API URL
./deployment/deploy.sh --api http://custom-api:8000

# Deploy for a specific environment
./deployment/deploy.sh --env prod
```

3. Access the application at http://localhost:8080

## Manual Deployment

If you prefer to deploy manually:

1. Modify the `deployment/config_variables.json` file with the required configuration values
2. Run the following command to build and start the containers:

```bash
docker compose -f deployment/docker-compose.yml up -d
```

## Troubleshooting

### Common Issues

#### 1. API Connection Issues

If the application cannot connect to the API, check:

- The API URL is correctly set in the `deployment/config_variables.json` file
- The API service is running and accessible from the container
- There are no network issues between the container and the API service

Fix:

```bash
# Use a direct IP address instead of a hostname
./deployment/deploy.sh --api http://*************:7999

# Or use localhost if the API is on the same host
./deployment/deploy.sh --api http://**************:7999
```

#### 2. General Troubleshooting

- Ensure the API service is running and accessible from the Docker network
- Verify that environment variables are correctly set in the `.env` file
- Check container logs:
  ```bash
  docker logs xpay-admin-web
  ```
- Restart the container:
  ```bash
  docker compose -f deployment/docker-compose.yml restart
  ```

## Scaling

To scale the application, you can set up a load balancer in front of the admin-web container.
