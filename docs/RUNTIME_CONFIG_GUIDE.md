# 运行时配置指南

## 概述

本项目现在支持运行时动态配置，允许在容器启动时根据环境变量和配置文件生成前端配置。这种方式比构建时配置更灵活，支持在不重新构建镜像的情况下修改配置。

## 配置架构

### 混合配置方案

1. **构建时配置** (`src/Conf.ts`)

   - 用于 UMI 构建时的配置需求
   - 作为运行时配置的后备方案

2. **运行时配置** (`/runtime-config.json`)
   - 容器启动时由 entrypoint 脚本生成
   - 前端应用动态加载并使用

### 配置优先级

1. **环境变量** (最高优先级)

   - 来源：Docker Compose、Kubernetes、CI/CD 系统
   - 适用场景：生产环境、不同环境的配置管理

2. **配置文件** (`config_variables.json`) (较低优先级)
   - 来源：项目中的 JSON 配置文件
   - 适用场景：开发环境、默认配置

## 使用方法

### 1. 通过环境变量配置 (推荐)

在 `docker-compose.yml` 中设置环境变量：

```yaml
services:
  admin-web:
    environment:
      - UMI_APP_API_URL=https://api-prod.example.com
      - UMI_APP_SDK_SERVER_URL=https://sso-prod.example.com
      - UMI_APP_SDK_CLIENT_ID=your-production-client-id
      - UMI_APP_SDK_ORGANIZATION_NAME=your-org
      - UMI_APP_SDK_APP_NAME=your-app
      - UMI_APP_SDK_REDIRECT_PATH=/callback
      # 可选：指定自定义配置文件路径
      - PATH_TO_SECRET_FILE=/app/custom-config.json
```

### 2. 通过配置文件

修改 `deployment/config_variables.json`：

```json
{
  "UMI_APP_API_URL": "https://api-dev.example.com",
  "UMI_APP_SDK_SERVER_URL": "https://sso-dev.example.com",
  "UMI_APP_SDK_CLIENT_ID": "dev-client-id",
  "UMI_APP_SDK_ORGANIZATION_NAME": "dev-org",
  "UMI_APP_SDK_APP_NAME": "dev-app",
  "UMI_APP_SDK_REDIRECT_PATH": "/callback"
}
```

### 3. 挂载外部配置文件

```yaml
services:
  admin-web:
    volumes:
      - ./custom-config.json:/app/config_variables.json
    environment:
      - PATH_TO_SECRET_FILE=/app/config_variables.json
```

## 配置参数说明

| 参数 | 描述 | 示例值 |
| --- | --- | --- |
| `UMI_APP_API_URL` | API 服务器地址 | `https://api.example.com` |
| `UMI_APP_SDK_SERVER_URL` | Casdoor 服务器地址 | `https://sso.example.com` |
| `UMI_APP_SDK_CLIENT_ID` | Casdoor 客户端 ID | `your-client-id` |
| `UMI_APP_SDK_ORGANIZATION_NAME` | Casdoor 组织名称 | `your-organization` |
| `UMI_APP_SDK_APP_NAME` | Casdoor 应用名称 | `your-app` |
| `UMI_APP_SDK_REDIRECT_PATH` | OAuth 回调路径 | `/callback` |

## 环境变量

| 变量名                | 描述         | 默认值                       |
| --------------------- | ------------ | ---------------------------- |
| `PATH_TO_SECRET_FILE` | 配置文件路径 | `/app/config_variables.json` |

## 工作流程

1. 容器启动时，entrypoint 脚本执行
2. 读取 `PATH_TO_SECRET_FILE` 指定的配置文件
3. 将配置文件中的值导出为环境变量（如果环境中未设置）
4. 生成 `/usr/share/nginx/html/runtime-config.json` 文件
5. 前端应用启动时加载运行时配置
6. 如果运行时配置加载失败，回退到构建时配置

## 测试

运行测试脚本验证配置生成：

```bash
./deployment/test_runtime_config.sh
```

## 故障排除

### 1. 检查容器日志

```bash
docker logs xpay-admin-web
```

查找 entrypoint 脚本的日志输出，确认配置文件是否正确生成。

### 2. 检查运行时配置文件

```bash
docker exec xpay-admin-web cat /usr/share/nginx/html/runtime-config.json
```

### 3. 验证环境变量优先级

容器日志会显示每个配置项的来源：

- `Using existing environment variable for 'KEY': value` - 来自环境变量
- `Set default for 'KEY' from config file` - 来自配置文件

## 迁移指南

从旧的构建时配置迁移到新的运行时配置：

1. 现有的 `src/Conf.ts` 文件保持不变（作为后备）
2. 在 `docker-compose.yml` 中添加环境变量
3. 重新启动容器
4. 验证新配置是否生效

新的配置方式完全向后兼容，无需修改现有代码。
