# Taskmaster-ai MCP 工具使用指南

## 概述

Taskmaster-ai MCP (Model Context Protocol) 是一套强大的 AI 驱动任务管理工具集，旨在帮助开发团队高效地管理软件项目的任务规划、跟踪和执行。它通过智能化的方式将复杂的项目需求分解为可执行的任务，并提供完整的任务生命周期管理功能。

## 核心功能

### 1. 项目初始化与配置

#### 初始化项目

```bash
# 使用 mcp__taskmaster-ai__initialize_project 工具
# 参数：
# - projectRoot: 项目根目录（必需）
# - addAliases: 是否添加 shell 别名（可选）
# - skipInstall: 是否跳过依赖安装（可选）
```

该工具会在项目中创建以下结构：

- `.taskmaster/` - 主配置目录
- `.taskmaster/docs/` - 文档目录（包括 PRD）
- `.taskmaster/tasks/` - 任务文件目录
- `.taskmaster/reports/` - 报告目录

#### 配置 AI 模型

```bash
# 使用 mcp__taskmaster-ai__models 工具
# 查看当前模型配置
# 设置主模型、备用模型或研究模型
```

支持的模型类型：

- **主模型（Main）**：用于任务生成和更新的主要模型
- **备用模型（Fallback）**：主模型失败时的备选方案
- **研究模型（Research）**：用于需要深度研究的操作

### 2. 任务生成与管理

#### 从 PRD 生成任务

```bash
# 使用 mcp__taskmaster-ai__parse_prd 工具
# 参数：
# - input: PRD 文档路径（默认：.taskmaster/docs/prd.txt）
# - numTasks: 生成的顶层任务数量（建议 10-50）
# - research: 是否启用研究模式
```

工作流程：

1. 在 `.taskmaster/docs/` 目录下创建 `prd.txt` 文件
2. 编写详细的产品需求文档
3. 运行解析工具自动生成任务列表
4. 生成的任务保存在 `.taskmaster/tasks/tasks.json`

#### 任务查询操作

**获取所有任务**

```bash
# 使用 mcp__taskmaster-ai__get_tasks 工具
# 参数：
# - status: 按状态过滤（pending/done/in-progress等）
# - withSubtasks: 是否包含子任务
```

**获取特定任务详情**

```bash
# 使用 mcp__taskmaster-ai__get_task 工具
# 参数：
# - id: 任务 ID
```

**查找下一个任务**

```bash
# 使用 mcp__taskmaster-ai__next_task 工具
# 自动根据依赖关系和状态找出下一个应该执行的任务
```

#### 任务创建与修改

**添加新任务**

```bash
# 使用 mcp__taskmaster-ai__add_task 工具
# 参数：
# - prompt: 任务描述（AI 生成）
# - title/description/details: 手动创建参数
# - dependencies: 依赖的任务 ID（逗号分隔）
# - priority: 优先级（high/medium/low）
```

**添加子任务**

```bash
# 使用 mcp__taskmaster-ai__add_subtask 工具
# 参数：
# - id: 父任务 ID
# - title/description: 子任务信息
```

**更新任务状态**

```bash
# 使用 mcp__taskmaster-ai__set_task_status 工具
# 参数：
# - id: 任务或子任务 ID（支持批量，如 "1,2,3"）
# - status: 新状态
```

支持的状态：

- `pending` - 待处理
- `in-progress` - 进行中
- `done` - 已完成
- `review` - 审核中
- `deferred` - 延迟
- `cancelled` - 已取消

### 3. 高级功能

#### 任务扩展

**扩展单个任务**

```bash
# 使用 mcp__taskmaster-ai__expand_task 工具
# 将一个任务分解为多个子任务
# 参数：
# - id: 要扩展的任务 ID
# - num: 生成的子任务数量
# - prompt: 额外的上下文信息
```

**批量扩展任务**

```bash
# 使用 mcp__taskmaster-ai__expand_all 工具
# 基于复杂度自动扩展所有待处理任务
```

#### 复杂度分析

**分析项目复杂度**

```bash
# 使用 mcp__taskmaster-ai__analyze_project_complexity 工具
# 参数：
# - ids: 要分析的任务 ID（逗号分隔）
# - threshold: 复杂度阈值（1-10）
# - research: 是否使用研究模式
```

**查看复杂度报告**

```bash
# 使用 mcp__taskmaster-ai__complexity_report 工具
# 显示任务复杂度分析结果和扩展建议
```

#### 依赖管理

**添加依赖关系**

```bash
# 使用 mcp__taskmaster-ai__add_dependency 工具
# 参数：
# - id: 依赖方任务 ID
# - dependsOn: 被依赖任务 ID
```

**验证依赖关系**

```bash
# 使用 mcp__taskmaster-ai__validate_dependencies 工具
# 检查循环依赖和无效引用
```

**修复依赖问题**

```bash
# 使用 mcp__taskmaster-ai__fix_dependencies 工具
# 自动修复检测到的依赖问题
```

### 4. 批量操作

#### 批量更新任务

```bash
# 使用 mcp__taskmaster-ai__update 工具
# 参数：
# - from: 起始任务 ID
# - prompt: 更新说明
# 从指定 ID 开始更新所有后续任务
```

#### 移动任务位置

```bash
# 使用 mcp__taskmaster-ai__move_task 工具
# 参数：
# - from: 源任务 ID（支持批量）
# - to: 目标位置 ID
```

#### 清理操作

```bash
# 删除任务
# 使用 mcp__taskmaster-ai__remove_task 工具

# 清除子任务
# 使用 mcp__taskmaster-ai__clear_subtasks 工具
```

## 最佳实践

### 1. 项目启动流程

1. **初始化项目**

   ```
   初始化 Taskmaster 项目结构
   配置所需的 AI 模型
   ```

2. **编写 PRD**

   - 在 `.taskmaster/docs/prd.txt` 中详细描述项目需求
   - 包含功能描述、技术要求、验收标准等

3. **生成任务**

   - 使用 `parse_prd` 工具生成初始任务列表
   - 根据项目规模设置合适的任务数量（10-50 个）

4. **优化任务结构**
   - 运行复杂度分析
   - 根据建议扩展高复杂度任务
   - 设置任务间的依赖关系

### 2. 日常任务管理

1. **查看待办任务**

   ```
   使用 get_tasks 查看所有待处理任务
   使用 next_task 找出下一个应执行的任务
   ```

2. **更新任务状态**

   ```
   开始任务时设置为 in-progress
   完成后及时更新为 done
   遇到问题时标记为 review
   ```

3. **任务细化**
   ```
   对复杂任务使用 expand_task 分解
   为子任务设置清晰的验收标准
   ```

### 3. 团队协作建议

1. **任务分配**

   - 使用优先级标记重要任务
   - 通过状态跟踪任务进度
   - 定期审查 deferred 状态的任务

2. **依赖管理**

   - 明确标注任务间的依赖关系
   - 定期验证依赖的有效性
   - 避免创建循环依赖

3. **进度跟踪**
   - 使用复杂度报告评估项目进度
   - 关注 in-progress 任务的数量
   - 及时处理阻塞的任务

## 注意事项

1. **模型配置**

   - 确保在 MCP 配置中设置了相应的 API 密钥
   - 研究模式可能产生额外费用，谨慎使用

2. **任务 ID 管理**

   - 任务 ID 格式：主任务使用数字（如 "1", "2"）
   - 子任务 ID 格式：父 ID.子 ID（如 "1.1", "1.2"）

3. **文件路径**

   - 所有路径参数必须使用绝对路径
   - projectRoot 参数通常指向项目根目录

4. **批量操作**
   - 批量操作支持逗号分隔的 ID 列表
   - 执行批量删除前请谨慎确认

## 故障排除

1. **初始化失败**

   - 检查 projectRoot 路径是否正确
   - 确保有写入权限

2. **模型调用失败**

   - 验证 API 密钥配置
   - 检查网络连接
   - 尝试使用备用模型

3. **任务生成问题**
   - 确保 PRD 文档格式正确
   - 调整 numTasks 参数
   - 启用 research 模式获得更好结果

## 总结

Taskmaster-ai MCP 工具集提供了从项目规划到执行的完整解决方案。通过 AI 驱动的智能化功能，它能够显著提升项目管理效率，特别适合需要将复杂需求分解为可执行任务的软件开发项目。合理使用这些工具，可以让团队更专注于任务执行，而非繁琐的管理工作。
