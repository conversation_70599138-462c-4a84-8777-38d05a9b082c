# 商户列表管理页面搜索功能修复

## 问题描述

商户列表管理页面的搜索条件点击搜索后没有在接口中使用，导致搜索功能无效。

## 问题原因分析

1. **列定义缺少搜索配置**：在 `constants.ts` 中的列定义没有正确配置搜索功能
2. **缺少参数映射**：ProTable 的搜索参数没有正确映射到 API 参数
3. **搜索字段不完整**：部分可搜索字段没有启用搜索功能

## 修复内容

### 1. 更新列定义 (`src/pages/Admin/MerchantListManagement/constants.ts`)

#### 添加搜索配置

- **商户名称**：添加 `search.transform` 映射到 `merchantName`
- **公司/业务名称**：添加 `search.transform` 映射到 `businessName`
- **邮箱**：添加 `search.transform` 映射到 `contactEmail`
- **电话**：添加 `search.transform` 映射到 `contactPhone`
- **状态**：添加 `search.transform` 映射到 `status`

#### 新增搜索字段

- **用户 ID**：隐藏表格列，仅在搜索表单中显示
- **创建时间范围**：日期范围选择器，映射到 `startTime` 和 `endTime`

#### 禁用不支持的搜索

- **商户账号**：API 不支持按账号搜索，设置 `search: false`

### 2. 完善参数映射 (`src/pages/Admin/MerchantListManagement/hooks.ts`)

#### 改进参数处理

- 添加对空值的过滤，避免发送无效参数
- 保持对旧参数名的兼容性（如 `name` 映射到 `merchantName`）
- 改进状态参数的转换逻辑

### 3. 更新类型定义 (`src/pages/Admin/MerchantListManagement/types.ts`)

#### 扩展参数接口

- 添加 `userEmail` 和 `userPhone` 字段映射
- 添加 `merchantName` 字段支持
- 保持向后兼容性

## 修复后的搜索功能

### 支持的搜索字段

1. **商户名称** - 模糊搜索
2. **公司/业务名称** - 模糊搜索
3. **邮箱** - 模糊搜索
4. **电话** - 模糊搜索
5. **状态** - 精确匹配（启用/禁用/待审核）
6. **用户 ID** - 精确匹配
7. **创建时间范围** - 日期范围搜索

### 参数映射关系

```typescript
ProTable 参数 -> API 参数
merchantName -> merchantName
businessName -> businessName
userEmail -> contactEmail
userPhone -> contactPhone
status -> status (转换为 boolean)
userId -> userId
dateRange -> startTime + endTime
```

## 测试验证

### 测试文件

创建了 `test-search.ts` 文件用于验证参数映射的正确性。

### 测试用例

1. 基本搜索（商户名称、业务名称）
2. 联系信息搜索（邮箱、电话）
3. 状态搜索
4. 日期范围搜索
5. 用户 ID 搜索

## 使用说明

### 开发者

1. 搜索参数会自动通过 `search.transform` 映射到正确的 API 参数
2. 空值会被自动过滤，不会发送到 API
3. 状态值会自动转换为 API 期望的 boolean 类型

### 用户

1. 在搜索表单中输入搜索条件
2. 点击"搜索"按钮
3. 搜索参数会正确传递给后端 API
4. 表格会显示过滤后的结果

## 注意事项

1. **API 限制**：商户账号搜索被禁用，因为 API 不支持此功能
2. **状态搜索**：待审核状态(-1)可能无法通过 API 的 boolean 状态参数过滤
3. **日期格式**：确保日期格式为 YYYY-MM-DD

## 相关文件

- `src/pages/Admin/MerchantListManagement/constants.ts` - 列定义和搜索配置
- `src/pages/Admin/MerchantListManagement/hooks.ts` - API 调用和参数映射
- `src/pages/Admin/MerchantListManagement/types.ts` - 类型定义
- `src/pages/Admin/MerchantListManagement/test-search.ts` - 测试文件
- `src/api/model/getApiSystemMerchantsParams.ts` - API 参数定义
