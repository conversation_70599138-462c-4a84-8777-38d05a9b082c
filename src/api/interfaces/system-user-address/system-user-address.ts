/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ListUserAddressesRes,
  GetApiSystemUserAddressesParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUserAddress = () => {
/**
 * @summary 查询用户充值地址列表
 */
const getApiSystemUserAddresses = (
    params: GetApiSystemUserAddressesParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListUserAddressesRes>(
      {url: `/api/system/user-addresses`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemUserAddresses}};
export type GetApiSystemUserAddressesResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserAddress>['getApiSystemUserAddresses']>>>
