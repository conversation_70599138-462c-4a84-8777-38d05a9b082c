/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1SearchTelegramGroupsRes,
  GetApiSystemTelegramGroupsSearchParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getTelegramGroups = () => {
/**
 * @summary Search telegram groups by title and chat_id
 */
const getApiSystemTelegramGroupsSearch = (
    params: GetApiSystemTelegramGroupsSearchParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1SearchTelegramGroupsRes>(
      {url: `/api/system/telegram-groups/search`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemTelegramGroupsSearch}};
export type GetApiSystemTelegramGroupsSearchResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTelegramGroups>['getApiSystemTelegramGroupsSearch']>>>
