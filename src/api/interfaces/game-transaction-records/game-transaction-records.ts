/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ExportGameTransactionRecordsReq,
  AdminApiApiSystemV1ExportGameTransactionRecordsRes,
  AdminApiApiSystemV1GetGameTransactionRecordsAggregateRes,
  AdminApiApiSystemV1GetGameTransactionRecordsDetailRes,
  AdminApiApiSystemV1GetGameTransactionRecordsListRes,
  AdminApiApiSystemV1GetGameTransactionRecordsStatsRes,
  AdminApiApiSystemV1UpdateGameTransactionRecordsCommissionStatusReq,
  AdminApiApiSystemV1UpdateGameTransactionRecordsCommissionStatusRes,
  GetApiSystemGameTransactionRecordsAggregateParams,
  GetApiSystemGameTransactionRecordsDetailParams,
  GetApiSystemGameTransactionRecordsListParams,
  GetApiSystemGameTransactionRecordsStatsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getGameTransactionRecords = () => {
/**
 * @summary Get aggregated transaction data
 */
const getApiSystemGameTransactionRecordsAggregate = (
    params?: GetApiSystemGameTransactionRecordsAggregateParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameTransactionRecordsAggregateRes>(
      {url: `/api/system/game-transaction-records/aggregate`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Get game transaction record detail
 */
const getApiSystemGameTransactionRecordsDetail = (
    params: GetApiSystemGameTransactionRecordsDetailParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameTransactionRecordsDetailRes>(
      {url: `/api/system/game-transaction-records/detail`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Export game transaction records
 */
const postApiSystemGameTransactionRecordsExport = (
    adminApiApiSystemV1ExportGameTransactionRecordsReq: AdminApiApiSystemV1ExportGameTransactionRecordsReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExportGameTransactionRecordsRes>(
      {url: `/api/system/game-transaction-records/export`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1ExportGameTransactionRecordsReq
    },
      );
    }
  /**
 * @summary Get game transaction records list
 */
const getApiSystemGameTransactionRecordsList = (
    params?: GetApiSystemGameTransactionRecordsListParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameTransactionRecordsListRes>(
      {url: `/api/system/game-transaction-records/list`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Get game transaction records statistics
 */
const getApiSystemGameTransactionRecordsStats = (
    params?: GetApiSystemGameTransactionRecordsStatsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameTransactionRecordsStatsRes>(
      {url: `/api/system/game-transaction-records/stats`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Update transaction commission status
 */
const putApiSystemGameTransactionRecordsUpdateCommissionStatus = (
    adminApiApiSystemV1UpdateGameTransactionRecordsCommissionStatusReq: AdminApiApiSystemV1UpdateGameTransactionRecordsCommissionStatusReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateGameTransactionRecordsCommissionStatusRes>(
      {url: `/api/system/game-transaction-records/update-commission-status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UpdateGameTransactionRecordsCommissionStatusReq
    },
      );
    }
  return {getApiSystemGameTransactionRecordsAggregate,getApiSystemGameTransactionRecordsDetail,postApiSystemGameTransactionRecordsExport,getApiSystemGameTransactionRecordsList,getApiSystemGameTransactionRecordsStats,putApiSystemGameTransactionRecordsUpdateCommissionStatus}};
export type GetApiSystemGameTransactionRecordsAggregateResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameTransactionRecords>['getApiSystemGameTransactionRecordsAggregate']>>>
export type GetApiSystemGameTransactionRecordsDetailResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameTransactionRecords>['getApiSystemGameTransactionRecordsDetail']>>>
export type PostApiSystemGameTransactionRecordsExportResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameTransactionRecords>['postApiSystemGameTransactionRecordsExport']>>>
export type GetApiSystemGameTransactionRecordsListResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameTransactionRecords>['getApiSystemGameTransactionRecordsList']>>>
export type GetApiSystemGameTransactionRecordsStatsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameTransactionRecords>['getApiSystemGameTransactionRecordsStats']>>>
export type PutApiSystemGameTransactionRecordsUpdateCommissionStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameTransactionRecords>['putApiSystemGameTransactionRecordsUpdateCommissionStatus']>>>
