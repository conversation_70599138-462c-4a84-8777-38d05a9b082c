/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ExportGameLiveBetDetailsReq,
  AdminApiApiSystemV1ExportGameLiveBetDetailsRes,
  AdminApiApiSystemV1GetGameLiveBetDetailsDetailRes,
  AdminApiApiSystemV1GetGameLiveBetDetailsListRes,
  AdminApiApiSystemV1GetGameLiveBetDetailsStatsRes,
  GetApiSystemGameLiveBetDetailsDetailParams,
  GetApiSystemGameLiveBetDetailsListParams,
  GetApiSystemGameLiveBetDetailsStatsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getGameLiveBetDetails = () => {
/**
 * @summary Get game live bet details detail
 */
const getApiSystemGameLiveBetDetailsDetail = (
    params: GetApiSystemGameLiveBetDetailsDetailParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameLiveBetDetailsDetailRes>(
      {url: `/api/system/game-live-bet-details/detail`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Export game live bet details
 */
const postApiSystemGameLiveBetDetailsExport = (
    adminApiApiSystemV1ExportGameLiveBetDetailsReq: AdminApiApiSystemV1ExportGameLiveBetDetailsReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExportGameLiveBetDetailsRes>(
      {url: `/api/system/game-live-bet-details/export`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1ExportGameLiveBetDetailsReq
    },
      );
    }
  /**
 * @summary Get game live bet details list
 */
const getApiSystemGameLiveBetDetailsList = (
    params?: GetApiSystemGameLiveBetDetailsListParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameLiveBetDetailsListRes>(
      {url: `/api/system/game-live-bet-details/list`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Get game live bet details statistics
 */
const getApiSystemGameLiveBetDetailsStats = (
    params?: GetApiSystemGameLiveBetDetailsStatsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameLiveBetDetailsStatsRes>(
      {url: `/api/system/game-live-bet-details/stats`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemGameLiveBetDetailsDetail,postApiSystemGameLiveBetDetailsExport,getApiSystemGameLiveBetDetailsList,getApiSystemGameLiveBetDetailsStats}};
export type GetApiSystemGameLiveBetDetailsDetailResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameLiveBetDetails>['getApiSystemGameLiveBetDetailsDetail']>>>
export type PostApiSystemGameLiveBetDetailsExportResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameLiveBetDetails>['postApiSystemGameLiveBetDetailsExport']>>>
export type GetApiSystemGameLiveBetDetailsListResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameLiveBetDetails>['getApiSystemGameLiveBetDetailsList']>>>
export type GetApiSystemGameLiveBetDetailsStatsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameLiveBetDetails>['getApiSystemGameLiveBetDetailsStats']>>>
