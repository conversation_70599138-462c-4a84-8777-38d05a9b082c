/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetPaymentRequestDetailRes,
  AdminApiApiSystemV1ListPaymentRequestRes,
  AdminApiApiSystemV1UpdatePaymentRequestStatusRes,
  GetApiSystemPaymentRequestsParams,
  PutApiSystemPaymentRequestsRequestIdStatusBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemPaymentRequest = () => {
/**
 * @summary 查询收款请求列表
 */
const getApiSystemPaymentRequests = (
    params: GetApiSystemPaymentRequestsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListPaymentRequestRes>(
      {url: `/api/system/payment-requests`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 查询收款请求详情
 */
const getApiSystemPaymentRequestsRequestId = (
    requestId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetPaymentRequestDetailRes>(
      {url: `/api/system/payment-requests/${requestId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 更新收款请求状态
 */
const putApiSystemPaymentRequestsRequestIdStatus = (
    requestId: number,
    putApiSystemPaymentRequestsRequestIdStatusBody: PutApiSystemPaymentRequestsRequestIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdatePaymentRequestStatusRes>(
      {url: `/api/system/payment-requests/${requestId}/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemPaymentRequestsRequestIdStatusBody
    },
      );
    }
  return {getApiSystemPaymentRequests,getApiSystemPaymentRequestsRequestId,putApiSystemPaymentRequestsRequestIdStatus}};
export type GetApiSystemPaymentRequestsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPaymentRequest>['getApiSystemPaymentRequests']>>>
export type GetApiSystemPaymentRequestsRequestIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPaymentRequest>['getApiSystemPaymentRequestsRequestId']>>>
export type PutApiSystemPaymentRequestsRequestIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemPaymentRequest>['putApiSystemPaymentRequestsRequestIdStatus']>>>
