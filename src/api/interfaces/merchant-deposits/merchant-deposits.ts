/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetMyDepositDetailRes,
  AdminApiApiSystemV1GetMyDepositsRes,
  GetApiSystemMyDepositsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getMerchantDeposits = () => {
/**
 * @summary 获取我的充值记录
 */
const getApiSystemMyDeposits = (
    params: GetApiSystemMyDepositsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyDepositsRes>(
      {url: `/api/system/my/deposits`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取我的充值记录详情
 */
const getApiSystemMyDepositsRechargesId = (
    rechargesId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyDepositDetailRes>(
      {url: `/api/system/my/deposits/${rechargesId}`, method: 'GET'
    },
      );
    }
  return {getApiSystemMyDeposits,getApiSystemMyDepositsRechargesId}};
export type GetApiSystemMyDepositsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantDeposits>['getApiSystemMyDeposits']>>>
export type GetApiSystemMyDepositsRechargesIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantDeposits>['getApiSystemMyDepositsRechargesId']>>>
