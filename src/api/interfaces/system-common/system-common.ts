/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetTokenSymbolsRes
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemCommon = () => {
/**
 * @summary 获取代币符号列表(用于搜索条件下拉框)
 */
const getApiSystemTokensSymbols = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetTokenSymbolsRes>(
      {url: `/api/system/tokens/symbols`, method: 'GET'
    },
      );
    }
  return {getApiSystemTokensSymbols}};
export type GetApiSystemTokensSymbolsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemCommon>['getApiSystemTokensSymbols']>>>
