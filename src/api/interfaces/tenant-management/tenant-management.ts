/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddTenantReq,
  AdminApiApiSystemV1AddTenantRes,
  AdminApiApiSystemV1DeleteTenantRes,
  AdminApiApiSystemV1EditTenantRes,
  AdminApiApiSystemV1GetTenantListRes,
  AdminApiApiSystemV1GetTenantRes,
  AdminApiApiSystemV1ResetTenant2FARes,
  AdminApiApiSystemV1UpdateTenantPasswordRes,
  AdminApiApiSystemV1UpdateTenantStatusReq,
  AdminApiApiSystemV1UpdateTenantStatusRes,
  DeleteApiSystemTenantsParams,
  GetApiSystemTenantsParams,
  PutApiSystemTenantsTenantIdBody,
  PutApiSystemTenantsTenantIdPasswordBody,
  PutApiSystemTenantsTenantIdReset2faBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getTenantManagement = () => {
/**
 * @summary 批量软删除租户
 */
const deleteApiSystemTenants = (
    params: DeleteApiSystemTenantsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteTenantRes>(
      {url: `/api/system/tenants`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取租户列表
 */
const getApiSystemTenants = (
    params: GetApiSystemTenantsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetTenantListRes>(
      {url: `/api/system/tenants`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 添加租户
 */
const postApiSystemTenants = (
    adminApiApiSystemV1AddTenantReq: AdminApiApiSystemV1AddTenantReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddTenantRes>(
      {url: `/api/system/tenants`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddTenantReq
    },
      );
    }
  /**
 * @summary 批量更新租户状态
 */
const putApiSystemTenantsBatchStatus = (
    adminApiApiSystemV1UpdateTenantStatusReq: AdminApiApiSystemV1UpdateTenantStatusReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateTenantStatusRes>(
      {url: `/api/system/tenants/batch/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UpdateTenantStatusReq
    },
      );
    }
  /**
 * @summary 获取单个租户详情
 */
const getApiSystemTenantsTenantId = (
    tenantId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetTenantRes>(
      {url: `/api/system/tenants/${tenantId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 编辑租户基础信息
 */
const putApiSystemTenantsTenantId = (
    tenantId: number,
    putApiSystemTenantsTenantIdBody: PutApiSystemTenantsTenantIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1EditTenantRes>(
      {url: `/api/system/tenants/${tenantId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemTenantsTenantIdBody
    },
      );
    }
  /**
 * @summary 修改指定租户密码
 */
const putApiSystemTenantsTenantIdPassword = (
    tenantId: number,
    putApiSystemTenantsTenantIdPasswordBody: PutApiSystemTenantsTenantIdPasswordBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateTenantPasswordRes>(
      {url: `/api/system/tenants/${tenantId}/password`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemTenantsTenantIdPasswordBody
    },
      );
    }
  /**
 * @summary 重置指定租户的 Google Authenticator
 */
const putApiSystemTenantsTenantIdReset2fa = (
    tenantId: number,
    putApiSystemTenantsTenantIdReset2faBody: PutApiSystemTenantsTenantIdReset2faBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ResetTenant2FARes>(
      {url: `/api/system/tenants/${tenantId}/reset-2fa`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemTenantsTenantIdReset2faBody
    },
      );
    }
  return {deleteApiSystemTenants,getApiSystemTenants,postApiSystemTenants,putApiSystemTenantsBatchStatus,getApiSystemTenantsTenantId,putApiSystemTenantsTenantId,putApiSystemTenantsTenantIdPassword,putApiSystemTenantsTenantIdReset2fa}};
export type DeleteApiSystemTenantsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['deleteApiSystemTenants']>>>
export type GetApiSystemTenantsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['getApiSystemTenants']>>>
export type PostApiSystemTenantsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['postApiSystemTenants']>>>
export type PutApiSystemTenantsBatchStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['putApiSystemTenantsBatchStatus']>>>
export type GetApiSystemTenantsTenantIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['getApiSystemTenantsTenantId']>>>
export type PutApiSystemTenantsTenantIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['putApiSystemTenantsTenantId']>>>
export type PutApiSystemTenantsTenantIdPasswordResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['putApiSystemTenantsTenantIdPassword']>>>
export type PutApiSystemTenantsTenantIdReset2faResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenantManagement>['putApiSystemTenantsTenantIdReset2fa']>>>
