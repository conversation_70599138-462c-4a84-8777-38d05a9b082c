/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1CreateGameCatalogReq,
  AdminApiApiSystemV1CreateGameCatalogRes,
  AdminApiApiSystemV1DeleteGameCatalogRes,
  AdminApiApiSystemV1GetGameCatalogDetailRes,
  AdminApiApiSystemV1GetGameCatalogListRes,
  AdminApiApiSystemV1UpdateGameCatalogReq,
  AdminApiApiSystemV1UpdateGameCatalogRes,
  AdminApiApiSystemV1UpdateGameCatalogStatusReq,
  AdminApiApiSystemV1UpdateGameCatalogStatusRes,
  DeleteApiSystemGameCatalogDeleteParams,
  GetApiSystemGameCatalogDetailParams,
  GetApiSystemGameCatalogListParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getGameCatalog = () => {
/**
 * @summary Create game catalog
 */
const postApiSystemGameCatalogCreate = (
    adminApiApiSystemV1CreateGameCatalogReq: AdminApiApiSystemV1CreateGameCatalogReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateGameCatalogRes>(
      {url: `/api/system/game-catalog/create`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateGameCatalogReq
    },
      );
    }
  /**
 * @summary Delete game catalog
 */
const deleteApiSystemGameCatalogDelete = (
    params: DeleteApiSystemGameCatalogDeleteParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteGameCatalogRes>(
      {url: `/api/system/game-catalog/delete`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary Get game catalog detail
 */
const getApiSystemGameCatalogDetail = (
    params: GetApiSystemGameCatalogDetailParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameCatalogDetailRes>(
      {url: `/api/system/game-catalog/detail`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Get game catalog list
 */
const getApiSystemGameCatalogList = (
    params?: GetApiSystemGameCatalogListParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameCatalogListRes>(
      {url: `/api/system/game-catalog/list`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Update game catalog
 */
const putApiSystemGameCatalogUpdate = (
    adminApiApiSystemV1UpdateGameCatalogReq: AdminApiApiSystemV1UpdateGameCatalogReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateGameCatalogRes>(
      {url: `/api/system/game-catalog/update`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UpdateGameCatalogReq
    },
      );
    }
  /**
 * @summary Update game catalog status
 */
const putApiSystemGameCatalogUpdateStatus = (
    adminApiApiSystemV1UpdateGameCatalogStatusReq: AdminApiApiSystemV1UpdateGameCatalogStatusReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateGameCatalogStatusRes>(
      {url: `/api/system/game-catalog/update-status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UpdateGameCatalogStatusReq
    },
      );
    }
  return {postApiSystemGameCatalogCreate,deleteApiSystemGameCatalogDelete,getApiSystemGameCatalogDetail,getApiSystemGameCatalogList,putApiSystemGameCatalogUpdate,putApiSystemGameCatalogUpdateStatus}};
export type PostApiSystemGameCatalogCreateResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameCatalog>['postApiSystemGameCatalogCreate']>>>
export type DeleteApiSystemGameCatalogDeleteResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameCatalog>['deleteApiSystemGameCatalogDelete']>>>
export type GetApiSystemGameCatalogDetailResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameCatalog>['getApiSystemGameCatalogDetail']>>>
export type GetApiSystemGameCatalogListResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameCatalog>['getApiSystemGameCatalogList']>>>
export type PutApiSystemGameCatalogUpdateResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameCatalog>['putApiSystemGameCatalogUpdate']>>>
export type PutApiSystemGameCatalogUpdateStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameCatalog>['putApiSystemGameCatalogUpdateStatus']>>>
