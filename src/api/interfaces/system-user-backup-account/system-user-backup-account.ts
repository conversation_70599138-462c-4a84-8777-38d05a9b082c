/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddUserBackupAccountRes,
  AdminApiApiSystemV1DeleteUserBackupAccountRes,
  AdminApiApiSystemV1GetBackupAccountsRes,
  AdminApiApiSystemV1GetUserBackupAccountsRes,
  AdminApiApiSystemV1SetUserBackupAccountVerificationRes,
  GetApiSystemBackupAccountsParams,
  GetApiSystemUsersUserIdBackupAccountsParams,
  PostApiSystemUsersUserIdBackupAccountsBody,
  PutApiSystemBackupAccountsAssociationIdVerificationBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUserBackupAccount = () => {
/**
 * @summary 获取备用账户列表
 */
const getApiSystemBackupAccounts = (
    params: GetApiSystemBackupAccountsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetBackupAccountsRes>(
      {url: `/api/system/backup-accounts`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 删除用户备用账户
 */
const deleteApiSystemBackupAccountsAssociationId = (
    associationId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteUserBackupAccountRes>(
      {url: `/api/system/backup-accounts/${associationId}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 设置备用账户验证状态
 */
const putApiSystemBackupAccountsAssociationIdVerification = (
    associationId: number,
    putApiSystemBackupAccountsAssociationIdVerificationBody: PutApiSystemBackupAccountsAssociationIdVerificationBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1SetUserBackupAccountVerificationRes>(
      {url: `/api/system/backup-accounts/${associationId}/verification`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemBackupAccountsAssociationIdVerificationBody
    },
      );
    }
  /**
 * @summary 获取用户备用账户列表
 */
const getApiSystemUsersUserIdBackupAccounts = (
    userId: number,
    params: GetApiSystemUsersUserIdBackupAccountsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetUserBackupAccountsRes>(
      {url: `/api/system/users/${userId}/backup-accounts`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 添加用户备用账户
 */
const postApiSystemUsersUserIdBackupAccounts = (
    userId: number,
    postApiSystemUsersUserIdBackupAccountsBody: PostApiSystemUsersUserIdBackupAccountsBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddUserBackupAccountRes>(
      {url: `/api/system/users/${userId}/backup-accounts`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: postApiSystemUsersUserIdBackupAccountsBody
    },
      );
    }
  return {getApiSystemBackupAccounts,deleteApiSystemBackupAccountsAssociationId,putApiSystemBackupAccountsAssociationIdVerification,getApiSystemUsersUserIdBackupAccounts,postApiSystemUsersUserIdBackupAccounts}};
export type GetApiSystemBackupAccountsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserBackupAccount>['getApiSystemBackupAccounts']>>>
export type DeleteApiSystemBackupAccountsAssociationIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserBackupAccount>['deleteApiSystemBackupAccountsAssociationId']>>>
export type PutApiSystemBackupAccountsAssociationIdVerificationResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserBackupAccount>['putApiSystemBackupAccountsAssociationIdVerification']>>>
export type GetApiSystemUsersUserIdBackupAccountsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserBackupAccount>['getApiSystemUsersUserIdBackupAccounts']>>>
export type PostApiSystemUsersUserIdBackupAccountsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserBackupAccount>['postApiSystemUsersUserIdBackupAccounts']>>>
