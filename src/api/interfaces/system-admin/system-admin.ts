/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetReferralCommissionListRes,
  GetApiSystemReferralCommissionsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemAdmin = () => {
/**
 * @summary 获取佣金记录列表
 */
const getApiSystemReferralCommissions = (
    params: GetApiSystemReferralCommissionsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetReferralCommissionListRes>(
      {url: `/api/system/referral-commissions`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemReferralCommissions}};
export type GetApiSystemReferralCommissionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAdmin>['getApiSystemReferralCommissions']>>>
