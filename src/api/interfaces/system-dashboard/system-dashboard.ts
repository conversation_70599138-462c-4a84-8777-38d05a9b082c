/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetDashboardStatsRes
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemDashboard = () => {
/**
 * @summary 获取管理后台首页统计信息
 */
const getApiSystemDashboardStats = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetDashboardStatsRes>(
      {url: `/api/system/dashboard/stats`, method: 'GET'
    },
      );
    }
  return {getApiSystemDashboardStats}};
export type GetApiSystemDashboardStatsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemDashboard>['getApiSystemDashboardStats']>>>
