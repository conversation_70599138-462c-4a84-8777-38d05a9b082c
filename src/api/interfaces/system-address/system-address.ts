/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetAddressStatisticsRes,
  AdminApiApiSystemV1GetImportProgressRes,
  AdminApiApiSystemV1ImportAddressesReq,
  AdminApiApiSystemV1ImportAddressesRes
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemAddress = () => {
/**
 * @summary 获取地址统计信息
 */
const getApiSystemAddressStatistics = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAddressStatisticsRes>(
      {url: `/api/system/address-statistics`, method: 'GET'
    },
      );
    }
  /**
 * @summary 导入地址数据
 */
const postApiSystemAddressesImport = (
    adminApiApiSystemV1ImportAddressesReq: AdminApiApiSystemV1ImportAddressesReq,
 ) => {const formData = new FormData();
if(adminApiApiSystemV1ImportAddressesReq.file !== undefined) {
 formData.append('file', adminApiApiSystemV1ImportAddressesReq.file)
 }

      return axiosInstance<AdminApiApiSystemV1ImportAddressesRes>(
      {url: `/api/system/addresses/import`, method: 'POST',
      headers: {'Content-Type': 'multipart/form-data', },
       data: formData
    },
      );
    }
  /**
 * @summary 获取地址导入进度
 */
const getApiSystemAddressesImportTaskIdProgress = (
    taskId: string,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetImportProgressRes>(
      {url: `/api/system/addresses/import/${taskId}/progress`, method: 'GET'
    },
      );
    }
  return {getApiSystemAddressStatistics,postApiSystemAddressesImport,getApiSystemAddressesImportTaskIdProgress}};
export type GetApiSystemAddressStatisticsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAddress>['getApiSystemAddressStatistics']>>>
export type PostApiSystemAddressesImportResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAddress>['postApiSystemAddressesImport']>>>
export type GetApiSystemAddressesImportTaskIdProgressResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAddress>['getApiSystemAddressesImportTaskIdProgress']>>>
