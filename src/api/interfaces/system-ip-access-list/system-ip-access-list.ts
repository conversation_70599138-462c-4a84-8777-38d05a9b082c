/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1AddIpAccessListReq,
  AdminApiApiSystemV1AddIpAccessListRes,
  AdminApiApiSystemV1DeleteIpAccessListRes,
  AdminApiApiSystemV1GetIpAccessListRes,
  AdminApiApiSystemV1PatchIpAccessListRes,
  GetApiSystemIpAccessListsParams,
  PatchApiSystemIpAccessListsIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemIpAccessList = () => {
/**
 * @summary 查询IP访问列表
 */
const getApiSystemIpAccessLists = (
    params: GetApiSystemIpAccessListsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetIpAccessListRes>(
      {url: `/api/system/ip-access-lists`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 添加IP到访问列表
 */
const postApiSystemIpAccessLists = (
    adminApiApiSystemV1AddIpAccessListReq: AdminApiApiSystemV1AddIpAccessListReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1AddIpAccessListRes>(
      {url: `/api/system/ip-access-lists`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1AddIpAccessListReq
    },
      );
    }
  /**
 * @summary 从访问列表删除IP
 */
const deleteApiSystemIpAccessListsId = (
    id: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteIpAccessListRes>(
      {url: `/api/system/ip-access-lists/${id}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 更新IP访问控制状态
 */
const patchApiSystemIpAccessListsId = (
    id: number,
    patchApiSystemIpAccessListsIdBody: PatchApiSystemIpAccessListsIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1PatchIpAccessListRes>(
      {url: `/api/system/ip-access-lists/${id}`, method: 'PATCH',
      headers: {'Content-Type': 'application/json', },
      data: patchApiSystemIpAccessListsIdBody
    },
      );
    }
  return {getApiSystemIpAccessLists,postApiSystemIpAccessLists,deleteApiSystemIpAccessListsId,patchApiSystemIpAccessListsId}};
export type GetApiSystemIpAccessListsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemIpAccessList>['getApiSystemIpAccessLists']>>>
export type PostApiSystemIpAccessListsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemIpAccessList>['postApiSystemIpAccessLists']>>>
export type DeleteApiSystemIpAccessListsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemIpAccessList>['deleteApiSystemIpAccessListsId']>>>
export type PatchApiSystemIpAccessListsIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemIpAccessList>['patchApiSystemIpAccessListsId']>>>
