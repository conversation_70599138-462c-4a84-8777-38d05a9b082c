/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetAdminTransferDetailRes,
  AdminApiApiSystemV1ListAdminTransfersRes,
  GetApiSystemTransfersParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemTransfer = () => {
/**
 * @summary 查询转账记录列表(后台)
 */
const getApiSystemTransfers = (
    params: GetApiSystemTransfersParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListAdminTransfersRes>(
      {url: `/api/system/transfers`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取转账记录详情(后台)
 */
const getApiSystemTransfersTransferId = (
    transferId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAdminTransferDetailRes>(
      {url: `/api/system/transfers/${transferId}`, method: 'GET'
    },
      );
    }
  return {getApiSystemTransfers,getApiSystemTransfersTransferId}};
export type GetApiSystemTransfersResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemTransfer>['getApiSystemTransfers']>>>
export type GetApiSystemTransfersTransferIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemTransfer>['getApiSystemTransfersTransferId']>>>
