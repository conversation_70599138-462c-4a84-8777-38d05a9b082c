/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetUserWithdrawDetailRes,
  AdminApiApiSystemV1ListUserWithdrawsRes,
  AdminApiApiSystemV1ReviewUserWithdrawRes,
  AdminApiApiSystemV1UpdateUserWithdrawStatusRes,
  GetApiSystemUserWithdrawsParams,
  PutApiSystemUserWithdrawsWithdrawIdReviewBody,
  PutApiSystemUserWithdrawsWithdrawIdStatusBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUserWithdraw = () => {
/**
 * @summary 获取提现记录列表
 */
const getApiSystemUserWithdraws = (
    params: GetApiSystemUserWithdrawsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ListUserWithdrawsRes>(
      {url: `/api/system/user-withdraws`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取提现记录详情
 */
const getApiSystemUserWithdrawsWithdrawId = (
    withdrawId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetUserWithdrawDetailRes>(
      {url: `/api/system/user-withdraws/${withdrawId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 审核提现记录
 */
const putApiSystemUserWithdrawsWithdrawIdReview = (
    withdrawId: number,
    putApiSystemUserWithdrawsWithdrawIdReviewBody: PutApiSystemUserWithdrawsWithdrawIdReviewBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ReviewUserWithdrawRes>(
      {url: `/api/system/user-withdraws/${withdrawId}/review`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUserWithdrawsWithdrawIdReviewBody
    },
      );
    }
  /**
 * @summary 更新提现记录状态
 */
const putApiSystemUserWithdrawsWithdrawIdStatus = (
    withdrawId: number,
    putApiSystemUserWithdrawsWithdrawIdStatusBody: PutApiSystemUserWithdrawsWithdrawIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateUserWithdrawStatusRes>(
      {url: `/api/system/user-withdraws/${withdrawId}/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemUserWithdrawsWithdrawIdStatusBody
    },
      );
    }
  return {getApiSystemUserWithdraws,getApiSystemUserWithdrawsWithdrawId,putApiSystemUserWithdrawsWithdrawIdReview,putApiSystemUserWithdrawsWithdrawIdStatus}};
export type GetApiSystemUserWithdrawsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['getApiSystemUserWithdraws']>>>
export type GetApiSystemUserWithdrawsWithdrawIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['getApiSystemUserWithdrawsWithdrawId']>>>
export type PutApiSystemUserWithdrawsWithdrawIdReviewResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['putApiSystemUserWithdrawsWithdrawIdReview']>>>
export type PutApiSystemUserWithdrawsWithdrawIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUserWithdraw>['putApiSystemUserWithdrawsWithdrawIdStatus']>>>
