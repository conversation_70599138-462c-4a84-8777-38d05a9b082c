/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1UploadAvatarReq,
  AdminApiApiSystemV1UploadAvatarRes,
  AdminApiApiSystemV1UploadFileReq,
  AdminApiApiSystemV1UploadFileRes,
  AdminApiApiSystemV1UploadImageReq,
  AdminApiApiSystemV1UploadImageRes,
  AdminApiApiSystemV1UploadVideoReq,
  AdminApiApiSystemV1UploadVideoRes
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemUpload = () => {
/**
 * @summary 上传头像
 */
const postApiSystemUploadAvatar = (
    adminApiApiSystemV1UploadAvatarReq: AdminApiApiSystemV1UploadAvatarReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UploadAvatarRes>(
      {url: `/api/system/upload/avatar`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UploadAvatarReq
    },
      );
    }
  /**
 * @summary 上传文件
 */
const postApiSystemUploadFile = (
    adminApiApiSystemV1UploadFileReq: AdminApiApiSystemV1UploadFileReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UploadFileRes>(
      {url: `/api/system/upload/file`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UploadFileReq
    },
      );
    }
  /**
 * @summary 上传图片
 */
const postApiSystemUploadImage = (
    adminApiApiSystemV1UploadImageReq: AdminApiApiSystemV1UploadImageReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UploadImageRes>(
      {url: `/api/system/upload/image`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UploadImageReq
    },
      );
    }
  /**
 * @summary 上传视频
 */
const postApiSystemUploadVideo = (
    adminApiApiSystemV1UploadVideoReq: AdminApiApiSystemV1UploadVideoReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UploadVideoRes>(
      {url: `/api/system/upload/video`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UploadVideoReq
    },
      );
    }
  return {postApiSystemUploadAvatar,postApiSystemUploadFile,postApiSystemUploadImage,postApiSystemUploadVideo}};
export type PostApiSystemUploadAvatarResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUpload>['postApiSystemUploadAvatar']>>>
export type PostApiSystemUploadFileResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUpload>['postApiSystemUploadFile']>>>
export type PostApiSystemUploadImageResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUpload>['postApiSystemUploadImage']>>>
export type PostApiSystemUploadVideoResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemUpload>['postApiSystemUploadVideo']>>>
