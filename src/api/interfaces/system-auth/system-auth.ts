/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1CasdoorSigninReq,
  AdminApiApiSystemV1CasdoorSigninRes,
  AdminApiApiSystemV1GetAdminInfoRes,
  AdminApiApiSystemV1GetCasdoorUserInfoRes,
  AdminApiApiSystemV1LoginReq,
  AdminApiApiSystemV1LoginRes,
  AdminApiApiSystemV1UpdateAdminInfoReq,
  AdminApiApiSystemV1UpdateAdminInfoRes
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemAuth = () => {
/**
 * @summary 获取管理员信息
 */
const getApiSystemAdminInfo = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetAdminInfoRes>(
      {url: `/api/system/admin-info`, method: 'GET'
    },
      );
    }
  /**
 * @summary 修改个人信息
 */
const putApiSystemAdminInfo = (
    adminApiApiSystemV1UpdateAdminInfoReq: AdminApiApiSystemV1UpdateAdminInfoReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateAdminInfoRes>(
      {url: `/api/system/admin-info`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1UpdateAdminInfoReq
    },
      );
    }
  /**
 * @summary Casdoor 用户信息
 */
const getApiSystemAuthCasdoorClaims = (
    
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetCasdoorUserInfoRes>(
      {url: `/api/system/auth/casdoor/claims`, method: 'GET'
    },
      );
    }
  /**
 * @summary Casdoor 单点登录
 */
const postApiSystemAuthCasdoorSignin = (
    adminApiApiSystemV1CasdoorSigninReq: AdminApiApiSystemV1CasdoorSigninReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CasdoorSigninRes>(
      {url: `/api/system/auth/casdoor/signin`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CasdoorSigninReq
    },
      );
    }
  /**
 * @summary 登录
 */
const postApiSystemLogin = (
    adminApiApiSystemV1LoginReq: AdminApiApiSystemV1LoginReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1LoginRes>(
      {url: `/api/system/login`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1LoginReq
    },
      );
    }
  return {getApiSystemAdminInfo,putApiSystemAdminInfo,getApiSystemAuthCasdoorClaims,postApiSystemAuthCasdoorSignin,postApiSystemLogin}};
export type GetApiSystemAdminInfoResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAuth>['getApiSystemAdminInfo']>>>
export type PutApiSystemAdminInfoResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAuth>['putApiSystemAdminInfo']>>>
export type GetApiSystemAuthCasdoorClaimsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAuth>['getApiSystemAuthCasdoorClaims']>>>
export type PostApiSystemAuthCasdoorSigninResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAuth>['postApiSystemAuthCasdoorSignin']>>>
export type PostApiSystemLoginResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemAuth>['postApiSystemLogin']>>>
