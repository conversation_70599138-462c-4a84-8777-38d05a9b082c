/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1DeleteApiKeyRes,
  AdminApiApiSystemV1GetApiKeyListRes,
  AdminApiApiSystemV1UpdateApiKeyRes,
  AdminApiApiSystemV1UpdateApiKeyStatusRes,
  DeleteApiSystemApikeysParams,
  GetApiSystemApikeysParams,
  PutApiSystemApikeysApiKeyIdBody,
  PutApiSystemApikeysApiKeyIdStatusBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getSystemApiKey = () => {
/**
 * @summary 删除API密钥
 */
const deleteApiSystemApikeys = (
    params: DeleteApiSystemApikeysParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteApiKeyRes>(
      {url: `/api/system/apikeys`, method: 'DELETE',
        params
    },
      );
    }
  /**
 * @summary 获取API密钥列表
 */
const getApiSystemApikeys = (
    params: GetApiSystemApikeysParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetApiKeyListRes>(
      {url: `/api/system/apikeys`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 更新API密钥
 */
const putApiSystemApikeysApiKeyId = (
    apiKeyId: number,
    putApiSystemApikeysApiKeyIdBody: PutApiSystemApikeysApiKeyIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateApiKeyRes>(
      {url: `/api/system/apikeys/${apiKeyId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemApikeysApiKeyIdBody
    },
      );
    }
  /**
 * @summary 更新API密钥状态
 */
const putApiSystemApikeysApiKeyIdStatus = (
    apiKeyId: number,
    putApiSystemApikeysApiKeyIdStatusBody: PutApiSystemApikeysApiKeyIdStatusBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateApiKeyStatusRes>(
      {url: `/api/system/apikeys/${apiKeyId}/status`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemApikeysApiKeyIdStatusBody
    },
      );
    }
  return {deleteApiSystemApikeys,getApiSystemApikeys,putApiSystemApikeysApiKeyId,putApiSystemApikeysApiKeyIdStatus}};
export type DeleteApiSystemApikeysResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemApiKey>['deleteApiSystemApikeys']>>>
export type GetApiSystemApikeysResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemApiKey>['getApiSystemApikeys']>>>
export type PutApiSystemApikeysApiKeyIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemApiKey>['putApiSystemApikeysApiKeyId']>>>
export type PutApiSystemApikeysApiKeyIdStatusResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getSystemApiKey>['putApiSystemApikeysApiKeyIdStatus']>>>
