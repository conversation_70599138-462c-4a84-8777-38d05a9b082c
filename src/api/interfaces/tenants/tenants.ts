/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1SearchTenantsRes,
  GetApiSystemTenantsSearchParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getTenants = () => {
/**
 * @summary Search tenants by username
 */
const getApiSystemTenantsSearch = (
    params: GetApiSystemTenantsSearchParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1SearchTenantsRes>(
      {url: `/api/system/tenants/search`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemTenantsSearch}};
export type GetApiSystemTenantsSearchResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTenants>['getApiSystemTenantsSearch']>>>
