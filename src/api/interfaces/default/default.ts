/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1DeleteAgentReq,
  AdminApiApiSystemV1DeleteAgentRes,
  DeleteApiSystemDeleteAgentParams,
  GetApiSystemDeleteAgentParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getDefault = () => {
const deleteApiSystemDeleteAgent = (
    params: DeleteApiSystemDeleteAgentParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAgentRes>(
      {url: `/api/system/delete-agent`, method: 'DELETE',
        params
    },
      );
    }
  const getApiSystemDeleteAgent = (
    params: GetApiSystemDeleteAgentParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAgentRes>(
      {url: `/api/system/delete-agent`, method: 'GET',
        params
    },
      );
    }
  const headApiSystemDeleteAgent = (
    adminApiApiSystemV1DeleteAgentReq: AdminApiApiSystemV1DeleteAgentReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAgentRes>(
      {url: `/api/system/delete-agent`, method: 'HEAD',
      headers: {'Content-Type': 'application/json', }
    },
      );
    }
  const patchApiSystemDeleteAgent = (
    adminApiApiSystemV1DeleteAgentReq: AdminApiApiSystemV1DeleteAgentReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAgentRes>(
      {url: `/api/system/delete-agent`, method: 'PATCH',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1DeleteAgentReq
    },
      );
    }
  const postApiSystemDeleteAgent = (
    adminApiApiSystemV1DeleteAgentReq: AdminApiApiSystemV1DeleteAgentReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAgentRes>(
      {url: `/api/system/delete-agent`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1DeleteAgentReq
    },
      );
    }
  const putApiSystemDeleteAgent = (
    adminApiApiSystemV1DeleteAgentReq: AdminApiApiSystemV1DeleteAgentReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteAgentRes>(
      {url: `/api/system/delete-agent`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1DeleteAgentReq
    },
      );
    }
  return {}};
