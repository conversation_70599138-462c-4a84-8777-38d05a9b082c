/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1ExportGameRngBetDetailsReq,
  AdminApiApiSystemV1ExportGameRngBetDetailsRes,
  AdminApiApiSystemV1GetGameRngBetDetailsDetailRes,
  AdminApiApiSystemV1GetGameRngBetDetailsListRes,
  AdminApiApiSystemV1GetGameRngBetDetailsStatsRes,
  GetApiSystemGameRngBetDetailsDetailParams,
  GetApiSystemGameRngBetDetailsListParams,
  GetApiSystemGameRngBetDetailsStatsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getGameRngBetDetails = () => {
/**
 * @summary Get RNG/FISH game bet details detail
 */
const getApiSystemGameRngBetDetailsDetail = (
    params: GetApiSystemGameRngBetDetailsDetailParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameRngBetDetailsDetailRes>(
      {url: `/api/system/game-rng-bet-details/detail`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Export RNG/FISH game bet details
 */
const postApiSystemGameRngBetDetailsExport = (
    adminApiApiSystemV1ExportGameRngBetDetailsReq: AdminApiApiSystemV1ExportGameRngBetDetailsReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1ExportGameRngBetDetailsRes>(
      {url: `/api/system/game-rng-bet-details/export`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1ExportGameRngBetDetailsReq
    },
      );
    }
  /**
 * @summary Get RNG/FISH game bet details list
 */
const getApiSystemGameRngBetDetailsList = (
    params?: GetApiSystemGameRngBetDetailsListParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameRngBetDetailsListRes>(
      {url: `/api/system/game-rng-bet-details/list`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary Get RNG/FISH game bet details statistics
 */
const getApiSystemGameRngBetDetailsStats = (
    params?: GetApiSystemGameRngBetDetailsStatsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetGameRngBetDetailsStatsRes>(
      {url: `/api/system/game-rng-bet-details/stats`, method: 'GET',
        params
    },
      );
    }
  return {getApiSystemGameRngBetDetailsDetail,postApiSystemGameRngBetDetailsExport,getApiSystemGameRngBetDetailsList,getApiSystemGameRngBetDetailsStats}};
export type GetApiSystemGameRngBetDetailsDetailResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameRngBetDetails>['getApiSystemGameRngBetDetailsDetail']>>>
export type PostApiSystemGameRngBetDetailsExportResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameRngBetDetails>['postApiSystemGameRngBetDetailsExport']>>>
export type GetApiSystemGameRngBetDetailsListResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameRngBetDetails>['getApiSystemGameRngBetDetailsList']>>>
export type GetApiSystemGameRngBetDetailsStatsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getGameRngBetDetails>['getApiSystemGameRngBetDetailsStats']>>>
