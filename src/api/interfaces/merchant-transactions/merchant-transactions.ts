/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1GetMyTransactionDetailRes,
  AdminApiApiSystemV1GetMyTransactionStatsRes,
  AdminApiApiSystemV1GetMyTransactionsRes,
  GetApiSystemMyTransactionsParams,
  GetApiSystemMyTransactionsStatsParams
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getMerchantTransactions = () => {
/**
 * @summary 获取我的资金记录
 */
const getApiSystemMyTransactions = (
    params: GetApiSystemMyTransactionsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyTransactionsRes>(
      {url: `/api/system/my/transactions`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取我的资金统计信息
 */
const getApiSystemMyTransactionsStats = (
    params?: GetApiSystemMyTransactionsStatsParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyTransactionStatsRes>(
      {url: `/api/system/my/transactions/stats`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 获取我的资金记录详情
 */
const getApiSystemMyTransactionsTransactionId = (
    transactionId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetMyTransactionDetailRes>(
      {url: `/api/system/my/transactions/${transactionId}`, method: 'GET'
    },
      );
    }
  return {getApiSystemMyTransactions,getApiSystemMyTransactionsStats,getApiSystemMyTransactionsTransactionId}};
export type GetApiSystemMyTransactionsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantTransactions>['getApiSystemMyTransactions']>>>
export type GetApiSystemMyTransactionsStatsResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantTransactions>['getApiSystemMyTransactionsStats']>>>
export type GetApiSystemMyTransactionsTransactionIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getMerchantTransactions>['getApiSystemMyTransactionsTransactionId']>>>
