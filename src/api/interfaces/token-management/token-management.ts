/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type {
  AdminApiApiSystemV1CreateTokenReq,
  AdminApiApiSystemV1CreateTokenRes,
  AdminApiApiSystemV1DeleteTokenRes,
  AdminApiApiSystemV1GetTokenDetailRes,
  AdminApiApiSystemV1GetTokenListRes,
  AdminApiApiSystemV1UpdateTokenRes,
  GetApiSystemTokensParams,
  PutApiSystemTokensTokenIdBody
} from '../../model';

import { axiosInstance } from '../../axiosInstance';



  export const getTokenManagement = () => {
/**
 * @summary 查询代币列表
 */
const getApiSystemTokens = (
    params: GetApiSystemTokensParams,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetTokenListRes>(
      {url: `/api/system/tokens`, method: 'GET',
        params
    },
      );
    }
  /**
 * @summary 创建代币
 */
const postApiSystemTokens = (
    adminApiApiSystemV1CreateTokenReq: AdminApiApiSystemV1CreateTokenReq,
 ) => {
      return axiosInstance<AdminApiApiSystemV1CreateTokenRes>(
      {url: `/api/system/tokens`, method: 'POST',
      headers: {'Content-Type': 'application/json', },
      data: adminApiApiSystemV1CreateTokenReq
    },
      );
    }
  /**
 * @summary 删除代币
 */
const deleteApiSystemTokensTokenId = (
    tokenId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1DeleteTokenRes>(
      {url: `/api/system/tokens/${tokenId}`, method: 'DELETE'
    },
      );
    }
  /**
 * @summary 获取代币详情
 */
const getApiSystemTokensTokenId = (
    tokenId: number,
 ) => {
      return axiosInstance<AdminApiApiSystemV1GetTokenDetailRes>(
      {url: `/api/system/tokens/${tokenId}`, method: 'GET'
    },
      );
    }
  /**
 * @summary 更新代币
 */
const putApiSystemTokensTokenId = (
    tokenId: number,
    putApiSystemTokensTokenIdBody: PutApiSystemTokensTokenIdBody,
 ) => {
      return axiosInstance<AdminApiApiSystemV1UpdateTokenRes>(
      {url: `/api/system/tokens/${tokenId}`, method: 'PUT',
      headers: {'Content-Type': 'application/json', },
      data: putApiSystemTokensTokenIdBody
    },
      );
    }
  return {getApiSystemTokens,postApiSystemTokens,deleteApiSystemTokensTokenId,getApiSystemTokensTokenId,putApiSystemTokensTokenId}};
export type GetApiSystemTokensResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTokenManagement>['getApiSystemTokens']>>>
export type PostApiSystemTokensResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTokenManagement>['postApiSystemTokens']>>>
export type DeleteApiSystemTokensTokenIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTokenManagement>['deleteApiSystemTokensTokenId']>>>
export type GetApiSystemTokensTokenIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTokenManagement>['getApiSystemTokensTokenId']>>>
export type PutApiSystemTokensTokenIdResult = NonNullable<Awaited<ReturnType<ReturnType<typeof getTokenManagement>['putApiSystemTokensTokenId']>>>
