/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemTokensOrderDirection } from './getApiSystemTokensOrderDirection';

export type GetApiSystemTokensParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 网络/链
 */
network?: string;
/**
 * 代币符号
 */
symbol?: string;
/**
 * 是否激活 (1:是, 0:否)
 */
isActive?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 排序字段 (默认 order)
 */
orderBy?: string;
/**
 * 排序方向 (asc/desc, 默认 asc)
 */
orderDirection?: GetApiSystemTokensOrderDirection;
};
