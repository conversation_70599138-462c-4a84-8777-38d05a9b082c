/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemMyNoticesIsRead } from './getApiSystemMyNoticesIsRead';

export type GetApiSystemMyNoticesParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 公告类型 (1:通知, 2:私信)
 */
type?: number;
/**
 * 标签 (0:普通, 1:重要, 2:活动)
 */
tag?: number;
/**
 * 阅读状态 (0:未读, 1:已读)
 */
isRead?: GetApiSystemMyNoticesIsRead;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
};
