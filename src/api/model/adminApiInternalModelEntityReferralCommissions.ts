/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiInternalModelEntityReferralCommissions {
  /** 佣金记录 ID (主键) */
  commissionId?: number;
  /** 关联的触发佣金的交易 ID (例如: 下级的某笔操作, 外键指向 transactions.transaction_id) */
  transactionId?: number;
  /** 获得佣金的用户 ID (外键, 指向 users.user_id) */
  referrerId?: number;
  /** 产生佣金的被推荐人用户 ID (外键, 指向 users.user_id) */
  inviteeId?: number;
  /** 佣金产生的推荐层级 */
  level?: number;
  /** 佣金金额 */
  commissionAmount?: GithubComShopspringDecimalDecimal;
  /** 佣金比率 (例如: 0.01 表示 1%) */
  commissionRate?: GithubComShopspringDecimalDecimal;
  /** 佣金代币 ID (外键, 指向 tokens.token_id) */
  tokenId?: number;
  /** 佣金状态: pending-待发放, paid-已发放, cancelled-已取消 */
  status?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 软删除时间 */
  deletedAt?: string;
}
