/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 产品类型 (swap, spot_limit)
 */
export type PatchApiSystemExchangeProductsProductIdBodyProductType =
  (typeof PatchApiSystemExchangeProductsProductIdBodyProductType)[keyof typeof PatchApiSystemExchangeProductsProductIdBodyProductType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiSystemExchangeProductsProductIdBodyProductType = {
  swap: 'swap',
  spot_limit: 'spot_limit',
} as const;
