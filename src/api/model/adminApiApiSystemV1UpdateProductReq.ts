/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdateProductReqFeeChargedIn } from './adminApiApiSystemV1UpdateProductReqFeeChargedIn';
import type { AdminApiApiSystemV1UpdateProductReqProductType } from './adminApiApiSystemV1UpdateProductReqProductType';

export interface AdminApiApiSystemV1UpdateProductReq {
  /**
   * 产品ID
   * @minimum 1
   */
  productId: number;
  /**
   * 基础代币ID
   * @minimum 1
   */
  baseTokenId: number;
  /**
   * 计价代币ID
   * @minimum 1
   */
  quoteTokenId: number;
  /**
   * 交易对符号 (例如: BTC/USDT)
   * @minLength 3
   * @maxLength 45
   */
  symbol: string;
  /** 产品类型 (swap, spot_limit) */
  productType: AdminApiApiSystemV1UpdateProductReqProductType;
  /** 产品是否激活可用 */
  isActive?: boolean;
  /** 是否允许买入基础代币 */
  allowBuy?: boolean;
  /** 是否允许卖出基础代币 */
  allowSell?: boolean;
  /**
   * 维护信息
   * @maxLength 512
   */
  maintenanceMessage?: string;
  /** 单笔最小兑换基础代币数量 */
  minBaseAmountPerTx: string;
  /** 单笔最大兑换基础代币数量 (空表示无限制) */
  maxBaseAmountPerTx?: string;
  /** 产品每日总兑换基础代币量上限 (空不限制) */
  dailyBaseVolumeLimit?: string;
  /** 产品累计总兑换基础代币量上限 (空不限制) */
  totalBaseVolumeLimit?: string;
  /**
   * 价格/汇率来源标识
   * @maxLength 50
   */
  priceSource?: string;
  /** 允许的最大价格滑点百分比 (例如 0.005) */
  allowedSlippagePercent?: string;
  /** 平台额外添加的价差率 (例如 0.001) */
  spreadRate: string;
  /**
   * 建议的价格刷新频率(秒)
   * @minimum 1
   */
  rateRefreshIntervalSec?: number;
  /** 标准兑换手续费率 (例如 0.002) */
  feeRate: string;
  /** 手续费扣除币种 (base, quote) */
  feeChargedIn: AdminApiApiSystemV1UpdateProductReqFeeChargedIn;
  /** 最低手续费(基础代币计价, 空不设最低) */
  minFeeAmountBaseEquivalent?: string;
  /** 显示排序 */
  displayOrder?: number;
  /** 产品描述 */
  description?: string;
  /** 用户可见性 */
  status?: boolean;
}
