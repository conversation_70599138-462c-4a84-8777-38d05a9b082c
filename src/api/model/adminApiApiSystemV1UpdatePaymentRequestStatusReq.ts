/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdatePaymentRequestStatusReqTargetStatus } from './adminApiApiSystemV1UpdatePaymentRequestStatusReqTargetStatus';

export interface AdminApiApiSystemV1UpdatePaymentRequestStatusReq {
  /** 收款请求ID */
  requestId: number;
  /** 目标状态 (3:已过期, 4:已取消) */
  targetStatus: AdminApiApiSystemV1UpdatePaymentRequestStatusReqTargetStatus;
  /** 操作备注 */
  remark?: string;
}
