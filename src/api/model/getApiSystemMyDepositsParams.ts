/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemMyDepositsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 商户ID (可选，不传则查询所有商户)
 */
merchantId?: number;
/**
 * 币种ID (筛选特定币种)
 */
tokenId?: number;
/**
 * 币种名称 (模糊搜索)
 */
tokenName?: string;
/**
 * 状态 (1-待确认/处理中, 2-已完成/已入账)
 */
state?: number;
/**
 * 交易哈希 (模糊搜索)
 */
txHash?: string;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 创建时间范围数组，格式：[开始时间, 结束时间]
 */
createdAt?: string[];
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
};
