/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PostApiSystemRedPacketImagesRedPacketImagesIdReviewBodyStatus } from './postApiSystemRedPacketImagesRedPacketImagesIdReviewBodyStatus';

export type PostApiSystemRedPacketImagesRedPacketImagesIdReviewBody = {
  /** 审核状态: success-通过, fail-拒绝 */
  status: PostApiSystemRedPacketImagesRedPacketImagesIdReviewBodyStatus;
  /** 拒绝原因(中文) */
  refuseReasonZh?: string;
  /** 拒绝原因(英文) */
  refuseReasonEn?: string;
};
