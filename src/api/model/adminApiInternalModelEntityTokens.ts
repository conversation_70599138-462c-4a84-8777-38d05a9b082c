/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiInternalModelEntityTokens {
  /** 代币内部 ID (主键) */
  tokenId?: number;
  /** 代币符号 (例如: USDT, BTC, ETH) */
  symbol?: string;
  /** 代币名称 (例如: Tether, Bitcoin, Ethereum) */
  name?: string;
  /** 所属网络/链 (例如: Ethereum, Tron, Bitcoin, BSC, Solana) */
  network?: string;
  /** 链 ID (主要用于 EVM 兼容链) */
  chainId?: number;
  /** 确认次数 */
  confirmations?: number;
  /** 代币合约地址 (对于非原生代币). 对于原生代币为 NULL. */
  contractAddress?: string;
  /** 代币标准 (例如: native, ERC20, TRC20, BEP20, SPL) */
  tokenStandard?: string;
  /** 代币精度/小数位数 */
  decimals?: number;
  /** 是否为稳定币 */
  isStablecoin?: number;
  /** 是否为法币 */
  isFiat?: number;
  /** 代币是否在系统内激活可用 (总开关) */
  isActive?: number;
  /** 是否允许充值 (设为 FALSE 即表示充值维护中) */
  allowDeposit?: number;
  /** 是否允许提现 (设为 FALSE 即表示提现维护中) */
  allowWithdraw?: number;
  /** 是否允许内部转账 */
  allowTransfer?: number;
  /** 是否允许内部收款 (通常与 allow_transfer 联动或独立控制) */
  allowReceive?: number;
  /** 是否允许发红包 */
  allowRedPacket?: number;
  /** 是否允许在交易对中使用 */
  allowTrading?: number;
  /** 维护信息 (当充值/提现/其他功能禁用时显示) */
  maintenanceMessage?: string;
  /** 提币手续费类型: fixed-固定金额, percent-百分比 */
  withdrawalFeeType?: string;
  /** 提币手续费金额/比例 (根据 fee_type 决定) */
  withdrawalFeeAmount?: string;
  /** 单笔最小充值金额 */
  minDepositAmount?: string;
  /** 单笔最大充值金额 (NULL 表示无限制) */
  maxDepositAmount?: string;
  /** 单笔最小提币金额 */
  minWithdrawalAmount?: string;
  /** 单笔最大提币金额 (NULL 表示无限制) */
  maxWithdrawalAmount?: string;
  /** 单笔最小转账金额 */
  minTransferAmount?: string;
  /** 单笔最大转账金额 (NULL 表示无限制) */
  maxTransferAmount?: string;
  /** 单笔最小收款金额 */
  minReceiveAmount?: string;
  /** 单笔最大收款金额 (NULL 表示无限制) */
  maxReceiveAmount?: string;
  /** 单个红包最小金额 */
  minRedPacketAmount?: string;
  /** 单个红包最大金额 (NULL 表示无限制) */
  maxRedPacketAmount?: string;
  /** 单次发放红包最大个数 (NULL 表示无限制) */
  maxRedPacketCount?: number;
  /** 单次发放红包最大总金额 (NULL 表示无限制) */
  maxRedPacketTotalAmount?: string;
  /** 代币 Logo 图片 URL */
  logoUrl?: string;
  /** 项目官方网站 URL */
  projectWebsite?: string;
  /** 代币或项目描述 */
  description?: string;
  /** 排序字段 (用于前端展示时的排序) */
  order?: number;
  /** 状态 0-下架 1-上架 */
  status?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 软删除时间 */
  deletedAt?: string;
}
