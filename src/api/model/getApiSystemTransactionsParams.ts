/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemTransactionsType } from './getApiSystemTransactionsType';
import type { GetApiSystemTransactionsDirection } from './getApiSystemTransactionsDirection';

export type GetApiSystemTransactionsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 是否导出：0不导出，1导出
 */
export?: number;
/**
 * 用户ID
 */
userId?: number;
/**
 * 用户名(模糊查询)
 */
username?: string;
/**
 * 用户账号(模糊查询)
 */
account?: string;
/**
 * 代币符号
 */
tokenSymbol?: string;
/**
 * 交易类型
 */
type?: GetApiSystemTransactionsType;
/**
 * 交易状态
 */
status?: string;
/**
 * 资金方向
 */
direction?: GetApiSystemTransactionsDirection;
/**
 * 交易ID
 */
transactionId?: number;
/**
 * 关联交易ID
 */
relatedTransactionId?: number;
/**
 * 日期范围 (YYYY-MM-DD,YYYY-MM-DD)
 */
dateRange?: string;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 名字 (模糊搜索)
 */
firstName?: string;
};
