/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiInternalModelEntityMerchantApiKeys {
  /** API 密钥内部 ID (主键) */
  apiKeyId?: number;
  /** 所属商户 ID (外键, 指向 merchants.merchant_id) */
  merchantId?: number;
  /** API Key (公开标识符, 用于请求时识别商户) */
  apiKey?: string;
  /** Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!) */
  secret?: string;
  /** Secret Key 的哈希值 (用于验证签名或进行身份验证, 不存储明文!) */
  secretHash?: string;
  /** 密钥标签/名称 (方便商户管理, 例如: "生产环境", "测试服务器") */
  label?: string;
  /** 密钥状态: active-可用, revoked-已撤销, expired-已过期 */
  status?: string;
  /** 授权范围 (例如: payment.create, balance.query, withdrawal.create, 使用逗号分隔或 JSON 格式) */
  scopes?: string;
  /** 允许调用此密钥的 IP 地址列表 (逗号分隔或 CIDR, NULL 表示不限制) */
  ipWhitelist?: string;
  /** 密钥过期时间 (NULL 表示永不过期) */
  expiresAt?: string;
  /** 最后使用时间 (用于审计和判断活跃度) */
  lastUsedAt?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 软删除时间 */
  deletedAt?: string;
  /** API调用频率限制(每分钟) */
  rateLimit?: number;
  /** 每日调用限制 */
  dailyLimit?: number;
}
