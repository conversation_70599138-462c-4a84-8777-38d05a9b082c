/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PostApiSystemExchangeProductsProductIdStatusBodyIsActive } from './postApiSystemExchangeProductsProductIdStatusBodyIsActive';
import type { PostApiSystemExchangeProductsProductIdStatusBodyStatus } from './postApiSystemExchangeProductsProductIdStatusBodyStatus';

export type PostApiSystemExchangeProductsProductIdStatusBody = {
  /** 是否激活 */
  isActive: PostApiSystemExchangeProductsProductIdStatusBodyIsActive;
  /** 状态 */
  status: PostApiSystemExchangeProductsProductIdStatusBodyStatus;
};
