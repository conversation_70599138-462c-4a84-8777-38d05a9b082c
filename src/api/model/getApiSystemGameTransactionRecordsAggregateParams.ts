/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemGameTransactionRecordsAggregateInterval } from './getApiSystemGameTransactionRecordsAggregateInterval';

export type GetApiSystemGameTransactionRecordsAggregateParams = {
/**
 * Provider code filter
 */
providerCode?: string;
/**
 * Game type filter
 */
gameType?: string;
/**
 * Currency filter
 */
currency?: string;
/**
 * Start date filter (YYYY-MM-DD)
 */
dateStart?: string;
/**
 * End date filter (YYYY-MM-DD)
 */
dateEnd?: string;
/**
 * Time interval (hour/day/week/month)
 */
interval?: GetApiSystemGameTransactionRecordsAggregateInterval;
};
