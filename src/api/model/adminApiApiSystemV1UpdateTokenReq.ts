/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdateTokenReqIsActive } from './adminApiApiSystemV1UpdateTokenReqIsActive';
import type { AdminApiApiSystemV1UpdateTokenReqAllowDeposit } from './adminApiApiSystemV1UpdateTokenReqAllowDeposit';
import type { AdminApiApiSystemV1UpdateTokenReqAllowWithdraw } from './adminApiApiSystemV1UpdateTokenReqAllowWithdraw';
import type { AdminApiApiSystemV1UpdateTokenReqAllowTransfer } from './adminApiApiSystemV1UpdateTokenReqAllowTransfer';
import type { AdminApiApiSystemV1UpdateTokenReqAllowReceive } from './adminApiApiSystemV1UpdateTokenReqAllowReceive';
import type { AdminApiApiSystemV1UpdateTokenReqAllowRedPacket } from './adminApiApiSystemV1UpdateTokenReqAllowRedPacket';
import type { AdminApiApiSystemV1UpdateTokenReqAllowTrading } from './adminApiApiSystemV1UpdateTokenReqAllowTrading';
import type { AdminApiApiSystemV1UpdateTokenReqWithdrawalFeeType } from './adminApiApiSystemV1UpdateTokenReqWithdrawalFeeType';

export interface AdminApiApiSystemV1UpdateTokenReq {
  /** 代币ID */
  token_id: number;
  /** 代币名称 */
  name?: string;
  /** 合约地址 */
  contract_address?: string;
  /** 代币标准 */
  token_standard?: string;
  /** Logo URL (需为有效URL) */
  logo_url?: string;
  /** 项目官网 (需为有效URL) */
  project_website?: string;
  /** 描述 */
  description?: string;
  /**
   * 排序 (需为非负整数)
   * @minimum 0
   */
  order?: number;
  /** 是否激活 (0:否, 1:是) */
  isActive?: AdminApiApiSystemV1UpdateTokenReqIsActive;
  /** 是否允许充值 (0:否, 1:是) */
  allowDeposit?: AdminApiApiSystemV1UpdateTokenReqAllowDeposit;
  /** 是否允许提现 (0:否, 1:是) */
  allowWithdraw?: AdminApiApiSystemV1UpdateTokenReqAllowWithdraw;
  /** 是否允许内部转账 (0:否, 1:是) */
  allowTransfer?: AdminApiApiSystemV1UpdateTokenReqAllowTransfer;
  /** 是否允许内部收款 (0:否, 1:是) */
  allowReceive?: AdminApiApiSystemV1UpdateTokenReqAllowReceive;
  /** 是否允许发红包 (0:否, 1:是) */
  allowRedPacket?: AdminApiApiSystemV1UpdateTokenReqAllowRedPacket;
  /** 是否允许在交易对中使用 (0:否, 1:是) */
  allowTrading?: AdminApiApiSystemV1UpdateTokenReqAllowTrading;
  /** 最小充值金额 (-1表示无限制) */
  minDepositAmount?: string;
  /** 最大充值金额 (-1表示无限制) */
  maxDepositAmount?: string;
  /** 最小提现金额 (-1表示无限制) */
  minWithdrawalAmount?: string;
  /** 最大提现金额 (-1表示无限制) */
  maxWithdrawalAmount?: string;
  /** 提现手续费类型 (fixed:固定, percent:百分比) */
  withdrawalFeeType?: AdminApiApiSystemV1UpdateTokenReqWithdrawalFeeType;
  /** 提现手续费金额/百分比 */
  withdrawalFeeAmount?: string;
}
