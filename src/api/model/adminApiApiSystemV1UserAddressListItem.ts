/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1UserAddressListItem {
  userAddressId?: number;
  /** 币种ID */
  tokenId?: number;
  /** 租户 id */
  tenantId?: number;
  /** 用户id */
  userId?: number;
  /** 备注 */
  lable?: string;
  /** 币种 */
  name?: string;
  /** 链 */
  chan?: string;
  /** 地址 */
  address?: string;
  /** 二维码url */
  qrCodeUrl?: string;
  createdAt?: string;
  updatedAt?: string;
  type?: string;
  /** PayBot系统中的地址ID */
  paybotAddressId?: string;
  /** 是否复用地址：0-新生成，1-复用 */
  isReused?: number;
  /** 二维码数据（Base64格式） */
  qrCodeData?: string;
  /** 地址状态 */
  status?: string;
  /** 充值次数统计 */
  depositCount?: number;
  /** 最后一次充值时间 */
  lastDepositAt?: string;
  /** 用户账号 */
  account?: string;
  /** 一级代理名称 */
  firstAgentName?: string;
  /** 二级代理名称 */
  secondAgentName?: string;
  /** 三级代理名称 */
  thirdAgentName?: string;
  /** 主Telegram ID */
  telegramId?: string;
  /** 主Telegram用户名 */
  telegramUsername?: string;
  /** 主备份账户名字 */
  firstName?: string;
}
