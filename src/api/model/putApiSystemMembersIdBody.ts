/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemMembersIdBodyStatus } from './putApiSystemMembersIdBodyStatus';

export type PutApiSystemMembersIdBody = {
  /** 手机号 (唯一) */
  mobile?: string;
  /** 头像URL */
  avatar?: string;
  /** 状态(0:禁用, 1:启用) */
  status: PutApiSystemMembersIdBodyStatus;
  /**
   * 真实姓名
   * @maxLength 50
   */
  realName?: string;
  /**
   * 角色ID列表
   * @minLength 1
   */
  roleIds: number[];
  /** 主角色ID (必须在RoleIds中) */
  primaryRoleId: number;
  /** 岗位ID列表 */
  postIds?: number[];
  /** 上级管理员ID (0表示无上级) */
  pid?: number;
  /**
   * 邀请码 (唯一, 可选)
   * @maxLength 10
   */
  inviteCode?: string;
  /**
   * 备注
   * @maxLength 200
   */
  remark?: string;
  /** 邮箱 (唯一) */
  email: string;
};
