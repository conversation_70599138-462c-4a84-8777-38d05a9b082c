/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 产品类型 (swap, spot_limit)
 */
export type AdminApiApiSystemV1UpdateProductReqProductType =
  (typeof AdminApiApiSystemV1UpdateProductReqProductType)[keyof typeof AdminApiApiSystemV1UpdateProductReqProductType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AdminApiApiSystemV1UpdateProductReqProductType = {
  swap: 'swap',
  spot_limit: 'spot_limit',
} as const;
