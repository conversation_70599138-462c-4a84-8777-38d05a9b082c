/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiInternalModelEntityAdminRole {
  /** 角色ID */
  id?: number;
  /** 角色名称 */
  name?: string;
  /** 角色权限字符串 */
  key?: string;
  /** 数据范围 */
  dataScope?: number;
  /** 自定义部门权限 */
  customDept?: GithubComGogfGfV2EncodingGjsonJson;
  /** 上级角色ID */
  pid?: number;
  /** 关系树等级 */
  level?: number;
  /** 关系树 */
  tree?: string;
  /** 备注 */
  remark?: string;
  /** 排序 */
  sort?: number;
  /** 角色状态 */
  status?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
}
