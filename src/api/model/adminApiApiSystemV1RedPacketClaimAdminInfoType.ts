/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiApiSystemV1RedPacketClaimAdminInfoType {
  /** 领取记录 ID (主键) */
  claimId?: number;
  /** 租户 id */
  tenantId?: number;
  /** 红包 ID (外键, 指向 red_packets.red_packet_id) */
  redPacketId?: number;
  /** 领取者用户 ID (外键, 指向 users.user_id) */
  claimerUserId?: number;
  /** 领取金额 */
  amount?: GithubComShopspringDecimalDecimal;
  /** 关联的资金入账交易 ID (外键, 指向 transactions.transaction_id) */
  transactionId?: number;
  /** 领取时间 */
  claimedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
  /** 发送方用户 ID (外键, 指向 users.user_id) */
  senderUserId?: number;
  /** 接收方用户 ID (外键, 指向 users.user_id) */
  receiverUserId?: number;
  /** 发送方用户名 */
  senderUsername?: string;
  /** 接收方用户名 */
  receiverUsername?: string;
  /** 红包状态 (pending: 待领取, claimed: 已领取, cancelled: 已取消) */
  status?: string;
  /** 代币符号 (例如: USDT, BTC, ETH) */
  symbol?: string;
  /** 是否已发送通知: 0-未发送, 1-已发送 */
  notificationSent?: number;
  /** 通知发送时间 */
  notificationSentAt?: string;
  claimerUsername?: string;
  redPacketMemo?: string;
  tokenSymbol?: string;
  tokenId?: number;
  tokenName?: string;
  redPacketType?: string;
  createdAt?: string;
  /** 领取方一级代理名称 */
  firstAgentName?: string;
  /** 领取方二级代理名称 */
  secondAgentName?: string;
  /** 领取方三级代理名称 */
  thirdAgentName?: string;
  /** 领取方Telegram ID */
  telegramId?: string;
  /** 领取方Telegram用户名 */
  telegramUsername?: string;
  /** 领取方真实姓名 */
  firstName?: string;
  /** 发送方一级代理名称 */
  senderFirstAgentName?: string;
  /** 发送方二级代理名称 */
  senderSecondAgentName?: string;
  /** 发送方三级代理名称 */
  senderThirdAgentName?: string;
  /** 发送方Telegram ID */
  senderTelegramId?: string;
  /** 发送方Telegram用户名 */
  senderTelegramUsername?: string;
  /** 发送方真实姓名 */
  senderFirstName?: string;
  /** 红包UUID */
  redPacketUuid?: string;
}
