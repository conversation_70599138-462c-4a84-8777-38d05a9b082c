/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemMyCallbacksParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 商户ID (可选，不传则查询所有商户)
 */
merchantId?: number;
/**
 * 回调类型 (deposit_success, withdraw_success等)
 */
callbackType?: string;
/**
 * 状态 (pending-待发送, success-成功, failed-失败)
 */
status?: string;
/**
 * 关联记录ID (如交易ID)
 */
relatedId?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 创建时间范围数组，格式：[开始时间, 结束时间]
 */
createdAt?: string[];
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
};
