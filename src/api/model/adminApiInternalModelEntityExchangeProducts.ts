/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiInternalModelEntityExchangeProducts {
  /** 兑换产品内部 ID (主键) */
  productId?: number;
  /** 基础代币 ID (用户卖出/花费的代币, 外键关联 tokens.token_id) */
  baseTokenId?: number;
  /** 计价代币 ID (用户买入/获得的代币, 外键关联 tokens.token_id) */
  quoteTokenId?: number;
  /** 产品交易对符号 (例如: BTC/USDT, ETH/BTC) */
  symbol?: string;
  /** 产品类型: swap-即时兑换, spot_limit-现货限价(若支持), 或其他自定义类型 */
  productType?: string;
  /** 该兑换产品是否激活可用 (总开关) */
  isActive?: number;
  /** 是否允许买入基础代币 (即 Quote->Base 的兑换) */
  allowBuy?: number;
  /** 是否允许卖出基础代币 (即 Base->Quote 的兑换) */
  allowSell?: number;
  /** 维护信息 (当产品不可用或部分功能禁用时显示) */
  maintenanceMessage?: string;
  /** 单笔最小兑换的基础代币数量 */
  minBaseAmountPerTx?: GithubComShopspringDecimalDecimal;
  /** 单笔最大兑换的基础代币数量 (NULL 表示无特定限制) */
  maxBaseAmountPerTx?: GithubComShopspringDecimalDecimal;
  /** 产品每日总兑换基础代币量上限 (平台风控, NULL 不限制) */
  dailyBaseVolumeLimit?: GithubComShopspringDecimalDecimal;
  /** 产品累计总兑换基础代币量上限 (平台风控, NULL 不限制) */
  totalBaseVolumeLimit?: GithubComShopspringDecimalDecimal;
  /** 价格/汇率来源标识 (例如: internal_oracle, binance_feed, weighted_average) */
  priceSource?: string;
  /** 允许的最大价格滑点百分比 (例如 0.0050 表示 0.5%). NULL 表示此产品无特定滑点设置 */
  allowedSlippagePercent?: GithubComShopspringDecimalDecimal;
  /** 平台在报价基础上额外添加的价差率 (例如 0.001 表示 0.1%) */
  spreadRate?: GithubComShopspringDecimalDecimal;
  /** 建议的价格刷新频率 (秒), 应用层参考 (NULL 表示无建议) */
  rateRefreshIntervalSec?: number;
  /** 标准兑换手续费率 (例如: 0.002 表示 0.2%) */
  feeRate?: GithubComShopspringDecimalDecimal;
  /** 手续费扣除币种: base-从花费的基础代币里扣, quote-从获得的计价代币里扣 */
  feeChargedIn?: string;
  /** 最低手续费 (以基础代币计价, NULL 不设最低) */
  minFeeAmountBaseEquivalent?: GithubComShopspringDecimalDecimal;
  /** 显示排序 (数字越小越靠前) */
  displayOrder?: number;
  /** 产品或交易对的描述信息 */
  description?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 软删除时间 */
  deletedAt?: string;
  /** 状态 */
  status?: number;
}
