/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemRolesIdDataScopeBodyDataScope } from './putApiSystemRolesIdDataScopeBodyDataScope';
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export type PutApiSystemRolesIdDataScopeBody = {
  /** 数据范围(1:全部, 2:本部门, 3:本部门及以下, 4:仅本人, 5:自定义) */
  dataScope: PutApiSystemRolesIdDataScopeBodyDataScope;
  /** 自定义部门ID列表 (当 dataScope=5 时需要) */
  customDept?: GithubComGogfGfV2EncodingGjsonJson;
};
