/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemAgentsAgentIdBodyStatus } from './putApiSystemAgentsAgentIdBodyStatus';

export type PutApiSystemAgentsAgentIdBody = {
  /**
   * 代理名称
   * @minLength 2
   * @maxLength 50
   */
  agentName: string;
  /** 邮箱 */
  email?: string;
  /** 手机号 */
  phoneNumber?: string;
  /**
   * 公司/业务注册名称
   * @maxLength 100
   */
  businessName?: string;
  /**
   * Telegram机器人名称
   * @maxLength 50
   */
  telegramBotName?: string;
  /**
   * Telegram机器人Token
   * @maxLength 200
   */
  telegramBotToken?: string;
  /** 状态 (0:禁用, 1:启用) */
  status: PutApiSystemAgentsAgentIdBodyStatus;
  /** Telegram账户ID */
  telegramAccount?: number;
};
