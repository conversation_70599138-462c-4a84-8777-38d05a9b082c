/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemLoginLogsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 日期范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 开始时间，格式：2025-01-01
 */
startTime?: string;
/**
 * 结束时间，格式：2025-01-31
 */
endTime?: string;
/**
 * IP地址，支持模糊查询
 */
ip?: string;
/**
 * 日志ID
 */
id?: number;
/**
 * 用户名，支持模糊查询
 */
username?: string;
/**
 * 状态：1成功，0失败，-1全部
 */
status?: number;
/**
 * 是否导出：0不导出，1导出
 */
export?: number;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 真实姓名 (模糊搜索)
 */
firstName?: string;
};
