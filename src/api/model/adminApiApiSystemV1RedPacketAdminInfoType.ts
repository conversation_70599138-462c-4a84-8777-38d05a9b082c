/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export interface AdminApiApiSystemV1RedPacketAdminInfoType {
  /** 红包 ID (主键) */
  redPacketId?: number;
  /** 租户 id */
  tenantId?: number;
  /** 红包唯一id */
  uuid?: string;
  /** 图片id 外键id 指向red_packet_images.red_packet_images_id */
  redPacketImagesId?: number;
  /** 创建者用户 ID (外键, 指向 users.user_id) */
  creatorUserId?: number;
  /** 创建者用户 ID (外键, 指向 users.user_id) */
  creatorUsername?: string;
  /** 创建者用户 ID (外键, 指向 users.user_id) */
  coverFileId?: string;
  /** 创建者用户 ID (外键, 指向 users.user_id) */
  thumbUrl?: string;
  /** 红包代币 ID (外键, 指向 tokens.token_id) */
  tokenId?: number;
  /** 红包总金额 */
  totalAmount?: GithubComShopspringDecimalDecimal;
  /** 红包总个数 */
  quantity?: number;
  /** 剩余金额 */
  remainingAmount?: GithubComShopspringDecimalDecimal;
  /** 剩余个数 */
  remainingQuantity?: number;
  /** 红包类型: random-随机金额, fixed-固定金额 */
  type?: string;
  /** 红包类型: private-私聊红包, group-群组红包 */
  redPacketType?: string;
  /** 抽奖状态: pending-等待开奖, drawn-已开奖, cancelled-已取消, NULL-非抽奖红包 */
  drawStatus?: string;
  /** 开奖时间: 幸运红包开奖的时间 */
  drawTime?: string;
  /** Telegram 聊天 ID: 群组红包的群组 ChatID */
  chatId?: number;
  /** Telegram 群组消息 ID: 用于更新群组红包消息 */
  groupMessageId?: number;
  /** 红包留言 */
  memo?: string;
  /** 红包状态: active-可领取, expired-已过期, empty-已领完, cancelled-已取消 */
  status?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 过期时间 */
  expiresAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
  /** 发送方用户 ID (外键, 指向 users.user_id) */
  senderUserId?: number;
  /** 代币符号 (例如: USDT, BTC, ETH) */
  symbol?: string;
  /** 关联的扣款交易流水 ID (外键, 指向 transactions.id) */
  transactionId?: number;
  /** 内联消息 ID，用于后续编辑 */
  messageId?: string;
  /** 是否支付 0 未支付 1 已支付 */
  isPay?: number;
  /** 流水金额 */
  bettingVolume?: GithubComShopspringDecimalDecimal;
  /** 流水天数 */
  bettingVolumeDays?: number;
  /** 群组名 */
  groupTitle?: string;
  /** 指定群组 */
  groupId?: string;
  /** 群组邀请链接 */
  groupInvitationLink?: string;
  /** 是否指定群组 */
  specifyGroup?: number;
  /** 是否需要流水 */
  specifyBetting?: number;
  /** 是否需要会员 */
  isPremium?: number;
  /** 是否需要验证码 */
  isVerificationCode?: number;
  tokenLogo?: string;
  /** 一级代理名称 */
  firstAgentName?: string;
  /** 二级代理名称 */
  secondAgentName?: string;
  /** 三级代理名称 */
  thirdAgentName?: string;
  /** 主Telegram ID */
  telegramId?: string;
  /** 主Telegram用户名 */
  telegramUsername?: string;
  /** 主备份账户名字 */
  firstName?: string;
}
