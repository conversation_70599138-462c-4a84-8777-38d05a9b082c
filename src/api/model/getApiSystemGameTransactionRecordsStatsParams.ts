/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemGameTransactionRecordsStatsParams = {
/**
 * Provider code filter
 */
providerCode?: string;
/**
 * Game type filter
 */
gameType?: string;
/**
 * Transaction type filter
 */
type?: string;
/**
 * Currency filter
 */
currency?: string;
/**
 * Start date filter (YYYY-MM-DD)
 */
dateStart?: string;
/**
 * End date filter (YYYY-MM-DD)
 */
dateEnd?: string;
/**
 * Group by field (provider/gameType/transactionType/date)
 */
groupBy?: string;
};
