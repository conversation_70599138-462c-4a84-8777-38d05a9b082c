/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiInternalModelEntityOperationLog {
  /** 日志ID */
  id?: number;
  /** 请求ID */
  reqId?: string;
  /** 用户类型 */
  memberType?: string;
  /** 用户ID */
  memberId?: number;
  /** 用户名 */
  username?: string;
  /** 操作模块 */
  module?: string;
  /** 操作名称 */
  action?: string;
  /** 请求方法 */
  requestMethod?: string;
  /** 请求URL */
  requestUrl?: string;
  /** 请求参数 */
  requestParams?: GithubComGogfGfV2EncodingGjsonJson;
  /** 响应数据 */
  response?: GithubComGogfGfV2EncodingGjsonJson;
  /** 操作时长(ms) */
  duration?: number;
  /** 操作IP */
  operationIp?: string;
  /** 省编码 */
  provinceId?: number;
  /** 市编码 */
  cityId?: number;
  /** UA信息 */
  userAgent?: string;
  /** 错误提示 */
  errMsg?: string;
  /** 状态 (1-成功 0-失败) */
  status?: number;
  /** 创建时间 (操作时间) */
  createdAt?: string;
  /** 修改时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
}
