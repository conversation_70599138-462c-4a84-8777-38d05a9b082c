/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemApikeysParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * API密钥ID (精确搜索)
 */
apiKeyId?: number;
/**
 * 商户ID (可选，不传则查询所有商户)
 */
merchantId?: number;
/**
 * 商户名称 (模糊搜索)
 */
merchantName?: string;
/**
 * API密钥 (模糊搜索)
 */
apiKey?: string;
/**
 * 标签/名称 (模糊搜索)
 */
label?: string;
/**
 * 状态 (active-可用, revoked-已撤销, expired-已过期)
 */
status?: string;
/**
 * 授权范围 (模糊搜索)
 */
scopes?: string;
/**
 * IP白名单 (模糊搜索)
 */
ipWhitelist?: string;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 过期时间 (格式: YYYY-MM-DD)
 */
expiresAt?: string;
/**
 * 最后使用时间 (格式: YYYY-MM-DD)
 */
lastUsedAt?: string;
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
};
