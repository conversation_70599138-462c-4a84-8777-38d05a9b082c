/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';

export type GetApiSystemGameRngBetDetailsListParams = {
/**
 * Game account username filter
 */
username?: string;
/**
 * Game code filter
 */
gameCode?: string;
/**
 * Game name filter
 */
gameName?: string;
/**
 * Game category filter (RNG, FISH)
 */
gameCategory?: string;
/**
 * Product type filter (16=RNG, others=FISH)
 */
productType?: number;
/**
 * Bet order number filter
 */
betOrderNo?: string;
/**
 * Currency filter (CNY, USD, etc.)
 */
currency?: string;
/**
 * Bet time start filter
 */
betTimeStart?: string;
/**
 * Bet time end filter
 */
betTimeEnd?: string;
/**
 * Transaction time start filter
 */
transactionTimeStart?: string;
/**
 * Transaction time end filter
 */
transactionTimeEnd?: string;
/**
 * Minimum bet amount filter
 */
minBetAmount?: GithubComShopspringDecimalDecimal;
/**
 * Maximum bet amount filter
 */
maxBetAmount?: GithubComShopspringDecimalDecimal;
/**
 * Minimum win amount filter
 */
minWinAmount?: GithubComShopspringDecimalDecimal;
/**
 * Maximum win amount filter
 */
maxWinAmount?: GithubComShopspringDecimalDecimal;
/**
 * Win/Loss type filter (win, loss, all)
 */
winLossType?: string;
/**
 * Search keyword for username, game name, or bet order
 */
keyword?: string;
/**
 * Filter by Telegram ID
 */
telegramId?: number;
/**
 * Filter by Telegram username
 */
telegramUsername?: string;
/**
 * Filter by first name
 */
firstName?: string;
/**
 * Filter by tenant username
 */
tenantUsername?: string;
/**
 * Page number
 */
page?: number;
/**
 * Page size
 */
pageSize?: number;
};
