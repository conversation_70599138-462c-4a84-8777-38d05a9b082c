/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemAdminNoticesIdReadStatusReadStatus } from './getApiSystemAdminNoticesIdReadStatusReadStatus';

export type GetApiSystemAdminNoticesIdReadStatusParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 阅读状态 (0:未读, 1:已读)
 */
readStatus?: GetApiSystemAdminNoticesIdReadStatusReadStatus;
/**
 * 用户名 (模糊查询)
 */
username?: string;
/**
 * 部门ID
 */
deptId?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
};
