/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemGameCatalogListParams = {
/**
 * Product type filter (EG5/PG/PP)
 */
productType?: string;
/**
 * Platform filter (flash/html5/all)
 */
platform?: string;
/**
 * Game type filter (RNG/LIVE/PVP)
 */
gameType?: string;
/**
 * Display status filter (1=active, 0=inactive)
 */
displayStatus?: number;
/**
 * Search keyword for game name or code
 */
keyword?: string;
/**
 * Page number
 */
page?: number;
/**
 * Page size
 */
pageSize?: number;
};
