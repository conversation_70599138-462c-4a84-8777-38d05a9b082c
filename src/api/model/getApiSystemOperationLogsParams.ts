/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemOperationLogsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 日期范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 开始时间，格式：2025-01-01
 */
startTime?: string;
/**
 * 结束时间，格式：2025-01-31
 */
endTime?: string;
/**
 * 操作IP，支持模糊查询
 */
operationIp?: string;
/**
 * 日志ID
 */
id?: number;
/**
 * 用户名，支持模糊查询
 */
username?: string;
/**
 * 操作模块，支持模糊查询
 */
module?: string;
/**
 * 操作名称，支持模糊查询
 */
action?: string;
/**
 * 请求方法
 */
requestMethod?: string;
/**
 * 状态：1成功，0失败，-1全部
 */
status?: number;
/**
 * 是否导出：0不导出，1导出
 */
export?: number;
};
