/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemPaymentRequestsStatus } from './getApiSystemPaymentRequestsStatus';

export type GetApiSystemPaymentRequestsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 收款请求ID
 */
requestId?: number;
/**
 * 收款发起者用户名 (模糊查询)
 */
requesterUsername?: string;
/**
 * 付款人用户名 (模糊查询)
 */
payerUsername?: string;
/**
 * 收款发起者账号 (模糊查询)
 */
requesterAccount?: string;
/**
 * 付款人账号 (模糊查询)
 */
payerAccount?: string;
/**
 * 代币符号 (模糊查询)
 */
tokenSymbol?: string;
/**
 * 收款请求状态 (1: 待支付, 2: 已支付, 3: 已过期, 4: 已取消)
 */
status?: GetApiSystemPaymentRequestsStatus;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 是否导出:0不导出,1导出
 */
export?: number;
/**
 * 一级代理名称 (模糊搜索)
 */
firstAgentName?: string;
/**
 * 二级代理名称 (模糊搜索)
 */
secondAgentName?: string;
/**
 * 三级代理名称 (模糊搜索)
 */
thirdAgentName?: string;
/**
 * Telegram ID (模糊搜索)
 */
telegramId?: string;
/**
 * Telegram用户名 (模糊搜索)
 */
telegramUsername?: string;
/**
 * 真实姓名 (模糊搜索)
 */
firstName?: string;
};
