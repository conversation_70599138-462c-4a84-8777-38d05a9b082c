/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { AdminApiApiSystemV1UpdateRoleDataScopeReqDataScope } from './adminApiApiSystemV1UpdateRoleDataScopeReqDataScope';
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiApiSystemV1UpdateRoleDataScopeReq {
  /** 角色ID */
  id: number;
  /** 数据范围(1:全部, 2:本部门, 3:本部门及以下, 4:仅本人, 5:自定义) */
  dataScope: AdminApiApiSystemV1UpdateRoleDataScopeReqDataScope;
  /** 自定义部门ID列表 (当 dataScope=5 时需要) */
  customDept?: GithubComGogfGfV2EncodingGjsonJson;
}
