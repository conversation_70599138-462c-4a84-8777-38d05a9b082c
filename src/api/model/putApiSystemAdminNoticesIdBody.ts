/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemAdminNoticesIdBodyType } from './putApiSystemAdminNoticesIdBodyType';
import type { PutApiSystemAdminNoticesIdBodyTag } from './putApiSystemAdminNoticesIdBodyTag';
import type { PutApiSystemAdminNoticesIdBodyStatus } from './putApiSystemAdminNoticesIdBodyStatus';
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export type PutApiSystemAdminNoticesIdBody = {
  /**
   * 公告标题
   * @minLength 1
   * @maxLength 100
   */
  title: string;
  /** 公告类型(1:通知, 2:私信) */
  type: PutApiSystemAdminNoticesIdBodyType;
  /** 标签(0:普通, 1:重要, 2:活动) */
  tag?: PutApiSystemAdminNoticesIdBodyTag;
  /** 公告内容 (HTML) */
  content: string;
  /** 公告状态(0:草稿, 1:发布, 2:停用) */
  status: PutApiSystemAdminNoticesIdBodyStatus;
  /** 接收者ID列表 (类型为私信时必填, JSON数组 [1, 2, 3]) */
  receiver?: GithubComGogfGfV2EncodingGjsonJson;
  /**
   * 备注
   * @maxLength 200
   */
  remark?: string;
  /**
   * 排序
   * @minimum 0
   */
  sort?: number;
};
