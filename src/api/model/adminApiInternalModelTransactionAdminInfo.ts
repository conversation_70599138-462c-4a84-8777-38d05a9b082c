/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComShopspringDecimalDecimal } from './githubComShopspringDecimalDecimal';
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiInternalModelTransactionAdminInfo {
  /** 交易记录 ID (主键) */
  transactionId?: number;
  /** 关联用户 ID */
  userId?: number;
  username?: string;
  /** 关联代币 ID */
  tokenId?: number;
  /** 交易类型: deposit, withdrawal, transfer, red_packet, payment, commission, system_adjust, etc. */
  type?: string;
  /** 钱包类型:  冻结frozen ，余额 available */
  walletType?: string;
  /** 资金方向: in-增加, out-减少 */
  direction?: string;
  /** 交易金额 (绝对值) */
  amount?: GithubComShopspringDecimalDecimal;
  /** 交易前余额快照 (对应钱包) */
  balanceBefore?: GithubComShopspringDecimalDecimal;
  /** 交易后余额快照 (对应钱包) */
  balanceAfter?: GithubComShopspringDecimalDecimal;
  /** 关联交易 ID (例如: 转账的对方记录) */
  relatedTransactionId?: number;
  /** 关联实体 ID (例如: 红包 ID, 提现订单 ID) */
  relatedEntityId?: number;
  /** 关联实体类型 (例如: red_packet, withdrawal_order) */
  relatedEntityType?: string;
  /** 交易状态: 1-成功, 0-失败 */
  status?: number;
  /** 交易备注/消息 (例如: 管理员调账原因) */
  memo?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 最后更新时间 */
  updatedAt?: string;
  /** 软删除时间 */
  deletedAt?: string;
  /** 代币符号 (例如: USDT, BTC, ETH) */
  symbol?: string;
  /** 业务唯一标识符，用于幂等性检查 */
  businessId?: string;
  /** 用户请求的原始金额 (用户输入的金额) */
  requestAmount?: GithubComShopspringDecimalDecimal;
  /** 用户请求的参考信息 (如转账备注、提现地址等) */
  requestReference?: string;
  /** 用户请求的元数据 (JSON格式存储扩展信息) */
  requestMetadata?: GithubComGogfGfV2EncodingGjsonJson;
  /** 请求来源 (telegram, web, api, admin等) */
  requestSource?: string;
  /** 用户请求的IP地址 */
  requestIp?: string;
  /** 用户请求的User-Agent */
  requestUserAgent?: string;
  /** 用户发起请求的时间戳 */
  requestTimestamp?: string;
  /** 交易处理完成时间 */
  processedAt?: string;
  /** 手续费金额 */
  feeAmount?: GithubComShopspringDecimalDecimal;
  /** 手续费类型 (fixed, percentage) */
  feeType?: string;
  /** 汇率 (如果涉及币种转换) */
  exchangeRate?: GithubComShopspringDecimalDecimal;
  /** 目标用户ID (转账、红包等操作的接收方) */
  targetUserId?: number;
  /** 目标用户名 (转账、红包等操作的接收方用户名) */
  targetUsername?: string;
  /** DTM全局事务ID */
  dtmGid?: string;
  /** DTM分支ID */
  dtmBranchId?: string;
  tenantId?: number;
  tokenSymbol?: string;
  /** 一级代理名称 */
  firstAgentName?: string;
  /** 二级代理名称 */
  secondAgentName?: string;
  /** 三级代理名称 */
  thirdAgentName?: string;
  /** 主Telegram ID */
  telegramId?: string;
  /** 主Telegram用户名 */
  telegramUsername?: string;
  /** 主备份账户名字 */
  firstName?: string;
}
