/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiApiSystemV1WalletListItem {
  /** 钱包记录唯一 ID */
  walletId?: number;
  /** 租户 ID (通过触发器自动填充) */
  tenantId?: number;
  /** ledger 钱包id */
  ledgerId?: string;
  /** 用户 ID (外键, 关联 users.user_id) */
  userId?: number;
  /** 代币 ID (外键, 关联 tokens.token_id) */
  tokenId?: number;
  /** 可用余额 */
  availableBalance?: number;
  /** 冻结余额 (例如: 挂单中, 提现处理中) */
  frozenBalance?: number;
  /** 精度 */
  decimalPlaces?: number;
  /** 钱包记录创建时间 */
  createdAt?: string;
  /** 余额最后更新时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
  /** 主Telegram ID */
  telegramId?: string;
  /** 类型 */
  type?: string;
  /** 代币符号 (例如: USDT, BTC, ETH) */
  symbol?: string;
  /** 用户账号 */
  account?: string;
  /** 格式化后的可用余额 */
  formattedAvailableBalance?: string;
  /** 格式化后的冻结余额 */
  formattedFrozenBalance?: string;
  /** 一级代理名称 */
  firstAgentName?: string;
  /** 二级代理名称 */
  secondAgentName?: string;
  /** 三级代理名称 */
  thirdAgentName?: string;
  /** 主Telegram用户名 */
  telegramUsername?: string;
  /** 主备份账户名字 */
  firstName?: string;
}
