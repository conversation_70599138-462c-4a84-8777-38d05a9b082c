/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemGameLiveBetDetailsListParams = {
/**
 * Filter by username
 */
username?: string;
/**
 * Filter by game code
 */
gameCode?: string;
/**
 * Filter by game category
 */
gameCategory?: string;
/**
 * Filter by bet order number
 */
betOrderNo?: string;
/**
 * Filter by session ID
 */
sessionId?: string;
/**
 * Filter by currency
 */
currency?: string;
/**
 * Filter by product type
 */
productType?: number;
/**
 * Filter by API status
 */
apiStatus?: number;
/**
 * Filter by bet time start (format: 2006-01-02 15:04:05)
 */
betTimeStart?: string;
/**
 * Filter by bet time end (format: 2006-01-02 15:04:05)
 */
betTimeEnd?: string;
/**
 * Filter by minimum bet amount
 */
minBetAmount?: string;
/**
 * Filter by maximum bet amount
 */
maxBetAmount?: string;
/**
 * Filter by minimum win amount
 */
minWinAmount?: string;
/**
 * Filter by maximum win amount
 */
maxWinAmount?: string;
/**
 * Filter by Telegram ID
 */
telegramId?: number;
/**
 * Filter by Telegram username
 */
telegramUsername?: string;
/**
 * Filter by first name
 */
firstName?: string;
/**
 * Filter by tenant username
 */
tenantUsername?: string;
/**
 * Page number
 */
page?: number;
/**
 * Page size
 */
pageSize?: number;
};
