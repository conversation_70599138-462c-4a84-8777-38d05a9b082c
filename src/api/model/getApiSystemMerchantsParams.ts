/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemMerchantsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 商户名称 (模糊搜索)
 */
merchantName?: string;
/**
 * 公司/业务注册名称 (模糊搜索)
 */
businessName?: string;
/**
 * 商户邮箱 (模糊搜索)
 */
email?: string;
/**
 * 联系电话 (模糊搜索)
 */
phone?: string;
/**
 * 状态 (-1:待审核, 0:禁用, 1:启用)
 */
status?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
};
