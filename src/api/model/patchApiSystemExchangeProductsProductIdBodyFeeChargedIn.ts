/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

/**
 * 手续费扣除币种 (base, quote)
 */
export type PatchApiSystemExchangeProductsProductIdBodyFeeChargedIn =
  (typeof PatchApiSystemExchangeProductsProductIdBodyFeeChargedIn)[keyof typeof PatchApiSystemExchangeProductsProductIdBodyFeeChargedIn];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiSystemExchangeProductsProductIdBodyFeeChargedIn = {
  base: 'base',
  quote: 'quote',
} as const;
