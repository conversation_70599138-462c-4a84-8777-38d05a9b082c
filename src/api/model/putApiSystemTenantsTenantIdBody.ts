/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { PutApiSystemTenantsTenantIdBodyStatus } from './putApiSystemTenantsTenantIdBodyStatus';

export type PutApiSystemTenantsTenantIdBody = {
  /**
   * 公司/业务注册名称
   * @maxLength 100
   */
  businessName?: string;
  /** Telegram账户ID */
  telegramAccount?: number;
  /**
   * Telegram机器人Token
   * @maxLength 200
   */
  telegramBotToken?: string;
  /** 租户级别 */
  level?: number;
  /** 官方群组 */
  group?: string;
  /** Telegram群组ID */
  telegramGroupsId?: string;
  /**
   * 租户名称
   * @minLength 2
   * @maxLength 50
   */
  tenantName: string;
  /** 邮箱 */
  email?: string;
  /** 状态 (0:禁用, 1:启用) */
  status: PutApiSystemTenantsTenantIdBodyStatus;
  /**
   * Telegram机器人名称
   * @maxLength 50
   */
  telegramBotName?: string;
  /** 客服 */
  customer?: string;
};
