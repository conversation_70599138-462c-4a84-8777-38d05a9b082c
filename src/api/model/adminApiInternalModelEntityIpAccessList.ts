/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiInternalModelEntityIpAccessList {
  /** 记录ID */
  id?: number;
  /** 用户ID (关联用户表) */
  userId?: number;
  /** 代理ID (关联代理表) */
  agentId?: number;
  /** 管理员ID (关联管理员表) */
  adminId?: number;
  /** 列表类型 (blacklist-黑名单, whitelist-白名单) */
  listType?: string;
  /** 适用类型 */
  useType?: string;
  /** 规则描述 (可选, 如添加原因或来源) */
  description?: string;
  /** IP地址 (支持IPv4和IPv6) */
  ipAddress?: string;
  /** 原因 (拉黑或加白) */
  reason?: string;
  /** 操作者 (用户名或系统标识) */
  addedBy?: string;
  /** 过期时间 (NULL表示永久) */
  expiresAt?: string;
  /** 是否启用 (1-启用, 0-禁用) */
  isEnabled?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 修改时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
}
