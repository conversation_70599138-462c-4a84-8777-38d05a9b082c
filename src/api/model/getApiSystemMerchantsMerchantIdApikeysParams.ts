/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemMerchantsMerchantIdApikeysParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * API密钥 (模糊搜索)
 */
apiKey?: string;
/**
 * 标签/名称 (模糊搜索)
 */
label?: string;
/**
 * 状态 (active-可用, revoked-已撤销, expired-已过期)
 */
status?: string;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
};
