/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemMyTransactionsParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 商户ID (可选，不传则查询所有商户)
 */
merchantId?: number;
/**
 * 代币符号筛选 (如: USDT, BTC, ETH)
 */
tokenSymbol?: string;
/**
 * 交易类型筛选 (如: deposit, withdrawal, transfer, system_adjust)
 */
type?: string;
/**
 * 资金方向筛选 (in-增加, out-减少)
 */
direction?: string;
/**
 * 钱包类型筛选 (available-可用余额, frozen-冻结余额)
 */
walletType?: string;
/**
 * 交易状态筛选 (1-成功, 0-失败)
 */
status?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
/**
 * 创建时间范围数组，格式：[开始时间, 结束时间]
 */
createdAt?: string[];
/**
 * 最小金额
 */
amountMin?: string;
/**
 * 最大金额
 */
amountMax?: string;
/**
 * 业务ID搜索
 */
businessId?: string;
/**
 * 关联实体类型筛选
 */
relatedEntityType?: string;
/**
 * 是否导出 (true-导出, false-不导出)
 */
export?: boolean;
};
