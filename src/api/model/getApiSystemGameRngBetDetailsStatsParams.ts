/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export type GetApiSystemGameRngBetDetailsStatsParams = {
/**
 * Game account username filter
 */
username?: string;
/**
 * Game code filter
 */
gameCode?: string;
/**
 * Game category filter (RNG, FISH)
 */
gameCategory?: string;
/**
 * Product type filter (16=RNG, others=FISH)
 */
productType?: number;
/**
 * Currency filter
 */
currency?: string;
/**
 * Bet time start filter
 */
betTimeStart?: string;
/**
 * Bet time end filter
 */
betTimeEnd?: string;
/**
 * Transaction time start filter
 */
transactionTimeStart?: string;
/**
 * Transaction time end filter
 */
transactionTimeEnd?: string;
/**
 * Group by field (game, user, date, currency)
 */
groupBy?: string;
};
