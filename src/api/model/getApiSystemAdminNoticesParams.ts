/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GetApiSystemAdminNoticesType } from './getApiSystemAdminNoticesType';

export type GetApiSystemAdminNoticesParams = {
/**
 * 页码
 */
page?: number;
/**
 * 每页数量
 */
pageSize?: number;
/**
 * 公告标题 (模糊查询)
 */
title?: string;
/**
 * 公告类型 (1:通知, 2:私信)
 */
type?: GetApiSystemAdminNoticesType;
/**
 * 标签 (0:普通, 1:重要, 2:活动)
 */
tag?: number;
/**
 * 公告状态 (0:草稿, 1:发布, 2:停用)
 */
status?: number;
/**
 * 创建人ID
 */
createdBy?: number;
/**
 * 创建时间范围，格式：2025-01-01,2025-01-31
 */
dateRange?: string;
};
