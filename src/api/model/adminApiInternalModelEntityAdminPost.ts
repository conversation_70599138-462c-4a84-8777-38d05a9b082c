/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */

export interface AdminApiInternalModelEntityAdminPost {
  /** 岗位ID */
  id?: number;
  /** 岗位编码 */
  code?: string;
  /** 岗位名称 */
  name?: string;
  /** 备注 */
  remark?: string;
  /** 排序 */
  sort?: number;
  /** 状态 */
  status?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 软删除的时间戳 */
  deletedAt?: string;
}
