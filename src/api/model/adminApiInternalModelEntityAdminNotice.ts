/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 */
import type { GithubComGogfGfV2EncodingGjsonJson } from './githubComGogfGfV2EncodingGjsonJson';

export interface AdminApiInternalModelEntityAdminNotice {
  /** 公告ID */
  id?: number;
  /** 公告标题 */
  title?: string;
  /** 公告类型 */
  type?: number;
  /** 标签 */
  tag?: number;
  /** 公告内容 */
  content?: string;
  /** 接收者 */
  receiver?: GithubComGogfGfV2EncodingGjsonJson;
  /** 备注 */
  remark?: string;
  /** 排序 */
  sort?: number;
  /** 公告状态 */
  status?: number;
  /** 发送人 */
  createdBy?: number;
  /** 修改人 */
  updatedBy?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 删除时间 */
  deletedAt?: string;
}
